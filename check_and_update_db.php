<?php
// Include database connection
require_once 'config/database.php';

echo "<h1>Database Structure Check and Update</h1>";

// Function to check if a column exists in a table
function columnExists($pdo, $table, $column) {
    $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
    $stmt->execute([$column]);
    return $stmt->rowCount() > 0;
}

// Check if the attendees table has the required columns
$requiredColumns = [
    'lastname' => 'VARCHAR(255)',
    'organization' => 'VARCHAR(255)',
    'group' => 'VARCHAR(255)',
    'mobile' => 'VARCHAR(50)'
];

$missingColumns = [];
foreach ($requiredColumns as $column => $type) {
    if (!columnExists($pdo, 'attendees', $column)) {
        $missingColumns[$column] = $type;
    }
}

// Display current table structure
$stmt = $pdo->query("DESCRIBE attendees");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Current Attendees Table Structure</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
    echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
    echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
    echo "</tr>";
}

echo "</table>";

// Update the table if needed
if (!empty($missingColumns)) {
    echo "<h2>Missing Columns</h2>";
    echo "<p>The following columns need to be added to the attendees table:</p>";
    echo "<ul>";
    foreach ($missingColumns as $column => $type) {
        echo "<li>$column ($type)</li>";
    }
    echo "</ul>";
    
    echo "<h2>Updating Table Structure</h2>";
    
    try {
        $pdo->beginTransaction();
        
        // Add missing columns
        foreach ($missingColumns as $column => $type) {
            $sql = "ALTER TABLE attendees ADD COLUMN `$column` $type";
            if ($column === 'lastname') {
                $sql .= " AFTER name";
            } elseif ($column === 'organization') {
                $sql .= " AFTER lastname";
            } elseif ($column === 'group') {
                $sql .= " AFTER organization";
            } elseif ($column === 'mobile') {
                $sql .= " AFTER `group`";
            }
            
            echo "<p>Executing: $sql</p>";
            $pdo->exec($sql);
            echo "<p>Column `$column` added successfully.</p>";
        }
        
        // Make department nullable if it's not already
        if (columnExists($pdo, 'attendees', 'department')) {
            $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE 'department'");
            $departmentCol = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($departmentCol['Null'] === 'NO') {
                $sql = "ALTER TABLE attendees MODIFY department VARCHAR(255) NULL";
                echo "<p>Executing: $sql</p>";
                $pdo->exec($sql);
                echo "<p>Column `department` modified to be nullable.</p>";
            }
        }
        
        $pdo->commit();
        echo "<p class='success'>Database structure updated successfully!</p>";
    } catch (PDOException $e) {
        $pdo->rollBack();
        echo "<p class='error'>Error updating database structure: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='success'>All required columns exist in the attendees table.</p>";
    
    // Check if department is nullable
    $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE 'department'");
    $departmentCol = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($departmentCol['Null'] === 'NO') {
        echo "<p>The department column is currently NOT NULL. Updating to allow NULL values...</p>";
        
        try {
            $sql = "ALTER TABLE attendees MODIFY department VARCHAR(255) NULL";
            $pdo->exec($sql);
            echo "<p class='success'>Column `department` modified to be nullable.</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>Error updating department column: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='success'>The department column is already nullable.</p>";
    }
}
?>
