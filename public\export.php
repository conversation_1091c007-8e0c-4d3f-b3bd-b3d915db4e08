<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();

// Get event_id and date from URL parameters
$eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;
$selectedDate = isset($_GET['date']) ? $_GET['date'] : null;
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Export Data</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Export</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Export Check-in Data</h5>

                <form id="exportForm" action="../api/export_api.php" method="POST">
                    <div class="mb-3">
                        <label for="eventSelect" class="form-label">Select Event *</label>
                        <select id="eventSelect" name="event_id" class="form-select" required>
                            <option value="">-- Select an event --</option>
                            <?php foreach ($events as $event): ?>
                                <option value="<?= $event['id'] ?>" <?= $eventId == $event['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($event['name']) ?>
                                    (<?= date('d M Y', strtotime($event['start_date'])) ?> - <?= date('d M Y', strtotime($event['end_date'])) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="dateSelect" class="form-label">Select Date (Optional)</label>
                        <select id="dateSelect" name="date" class="form-select">
                            <option value="">All Dates</option>
                            <!-- Dates will be populated via JavaScript when an event is selected -->
                        </select>
                        <div class="form-text">Leave empty to export data for all dates.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Export Format *</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="formatCSV" value="csv" checked>
                            <label class="form-check-label" for="formatCSV">
                                CSV (.csv)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="formatExcel" value="excel">
                            <label class="form-check-label" for="formatExcel">
                                Excel (.xlsx)
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Export Type *</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="type" id="typeAttendees" value="attendees" checked>
                            <label class="form-check-label" for="typeAttendees">
                                All Attendees (with check-in status)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="type" id="typeCheckins" value="checkins">
                            <label class="form-check-label" for="typeCheckins">
                                Only Checked-in Attendees
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="type" id="typeSummary" value="summary">
                            <label class="form-check-label" for="typeSummary">
                                Summary Report (daily statistics)
                            </label>
                        </div>
                    </div>

                    <div class="mb-3" id="fieldsSection">
                        <label class="form-label">Select Fields to Export</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldNumber" value="number" checked>
                                    <label class="form-check-label" for="fieldNumber">Number (No.)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldName" value="name" checked>
                                    <label class="form-check-label" for="fieldName">Name</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldLastname" value="lastname">
                                    <label class="form-check-label" for="fieldLastname">Last Name</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldPosition" value="position">
                                    <label class="form-check-label" for="fieldPosition">Position</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldCompany" value="company">
                                    <label class="form-check-label" for="fieldCompany">Company</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldGroup" value="group">
                                    <label class="form-check-label" for="fieldGroup">Group</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldEventName" value="event_name" checked>
                                    <label class="form-check-label" for="fieldEventName">Event Name</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldEventDate" value="event_date" checked>
                                    <label class="form-check-label" for="fieldEventDate">Event Date</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldCheckinStatus" value="checkin_status" checked>
                                    <label class="form-check-label" for="fieldCheckinStatus">Check-in Status</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldCheckinTime" value="checkin_time" checked>
                                    <label class="form-check-label" for="fieldCheckinTime">Check-in Timestamp</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="button" id="exportButton" class="btn btn-primary">Export Data</button>
                    </div>

                    <!-- Status message area -->
                    <div id="exportStatus" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span id="statusMessage">Preparing export...</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Populate dates when an event is selected
document.getElementById('eventSelect').addEventListener('change', function() {
    const eventId = this.value;
    const dateSelect = document.getElementById('dateSelect');

    // Clear existing options except the first one
    while (dateSelect.options.length > 1) {
        dateSelect.remove(1);
    }

    if (eventId) {
        // Fetch dates for the selected event
        fetch(`../api/create_event.php?action=get_dates&event_id=${eventId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.dates) {
                    data.dates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date.value;
                        option.text = date.label;
                        dateSelect.add(option);
                    });
                }
            })
            .catch(error => console.error('Error fetching dates:', error));
    }
});

// Handle export type change
document.querySelectorAll('input[name="type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const fieldsSection = document.getElementById('fieldsSection');
        const exportType = this.value;

        // Show/hide fields section based on export type
        if (exportType === 'summary') {
            fieldsSection.style.display = 'none';
        } else {
            fieldsSection.style.display = 'block';

            // Enable/disable fields based on export type
            const checkinStatusField = document.getElementById('fieldCheckinStatus');

            if (exportType === 'checkins') {
                // For "Only Checked-in Attendees", check-in status is always "Checked In"
                checkinStatusField.checked = false;
                checkinStatusField.disabled = true;
            } else {
                checkinStatusField.disabled = false;
            }
        }
    });
});

// Initialize fields visibility based on default selection
window.addEventListener('DOMContentLoaded', function() {
    const defaultType = document.querySelector('input[name="type"]:checked').value;
    if (defaultType === 'summary') {
        document.getElementById('fieldsSection').style.display = 'none';
    }

    // If event_id is provided in URL, trigger the change event to load dates
    const eventSelect = document.getElementById('eventSelect');
    if (eventSelect.value) {
        // Trigger the change event to load dates
        const changeEvent = new Event('change');
        eventSelect.dispatchEvent(changeEvent);

        // Set the selected date if provided in URL
        const urlParams = new URLSearchParams(window.location.search);
        const dateParam = urlParams.get('date');

        if (dateParam) {
            // Wait for dates to load before selecting
            setTimeout(function() {
                const dateSelect = document.getElementById('dateSelect');
                for (let i = 0; i < dateSelect.options.length; i++) {
                    if (dateSelect.options[i].value === dateParam) {
                        dateSelect.selectedIndex = i;
                        break;
                    }
                }
            }, 500); // Wait 500ms for the AJAX request to complete
        }
    }

    // Handle export button click
    document.getElementById('exportButton').addEventListener('click', function() {
        // Show status message
        const statusElement = document.getElementById('exportStatus');
        const statusMessage = document.getElementById('statusMessage');
        statusElement.style.display = 'block';
        statusMessage.textContent = 'Preparing export...';

        // Get form data
        const form = document.getElementById('exportForm');
        const formData = new FormData(form);

        // Create a hidden iframe for download
        let iframe = document.getElementById('downloadFrame');
        if (!iframe) {
            iframe = document.createElement('iframe');
            iframe.id = 'downloadFrame';
            iframe.name = 'downloadFrame';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
        }

        // Set form target to iframe
        form.target = 'downloadFrame';

        // Submit the form
        form.submit();

        // Update status message
        setTimeout(function() {
            statusMessage.textContent = 'Export complete! Your download should start automatically.';
            statusElement.querySelector('.alert').classList.remove('alert-info');
            statusElement.querySelector('.alert').classList.add('alert-success');
            statusElement.querySelector('.spinner-border').style.display = 'none';

            // Hide status after 5 seconds
            setTimeout(function() {
                statusElement.style.display = 'none';
                // Reset alert classes
                statusElement.querySelector('.alert').classList.add('alert-info');
                statusElement.querySelector('.alert').classList.remove('alert-success');
                statusElement.querySelector('.spinner-border').style.display = 'inline-block';
            }, 5000);
        }, 1500);
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>