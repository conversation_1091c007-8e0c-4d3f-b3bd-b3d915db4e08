<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();
?>

<div class="row mb-4">
    <div class="col-md-8">
        <h1>Events</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Events</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <?php if (isAdmin()): ?>
        <a href="event_create.php" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Create New Event
        </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <?php if (count($events) > 0): ?>
        <?php foreach ($events as $event): ?>
            <div class="col-md-4 mb-4">
                <div class="card event-card">
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($event['name']) ?></h5>
                        <p class="card-text event-dates">
                            <i class="bi bi-calendar"></i>
                            <?= date('d M Y', strtotime($event['start_date'])) ?> -
                            <?= date('d M Y', strtotime($event['end_date'])) ?>
                        </p>
                        <p class="card-text"><?= htmlspecialchars($event['description']) ?></p>
                        <div class="d-flex justify-content-between">
                            <a href="checkin.php?event_id=<?= $event['id'] ?>" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right"></i> Check-in
                            </a>
                            <a href="upload_csv.php?event_id=<?= $event['id'] ?>" class="btn btn-secondary">
                                <i class="bi bi-upload"></i> Upload CSV
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="col-md-12">
            <div class="alert alert-info">
                <?php if (isAdmin()): ?>
                No events found. <a href="event_create.php" class="alert-link">Create your first event</a>.
                <?php else: ?>
                No events found. Please contact an administrator to create events.
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>