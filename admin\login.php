<?php
// Start session
session_start();

// Include functions
require_once '../config/functions.php';

// Check if already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // Debug information
    $debug = "Username: $username<br>";

    if (empty($username) || empty($password)) {
        $error = "Username and password are required.";
    } else {
        // Check credentials
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        // Debug information
        $debug .= "User found: " . ($user ? "Yes" : "No") . "<br>";
        if ($user) {
            $debug .= "Stored password hash: " . $user['password'] . "<br>";
            $debug .= "Password verification result: " . (password_verify($password, $user['password']) ? "Success" : "Failed") . "<br>";
        }

        if ($user && password_verify($password, $user['password'])) {
            // Login successful
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];

            // Redirect to dashboard
            redirect('index.php');
        } else {
            $error = "Invalid username or password.";
        }
    }

    // Display debug information (remove in production)
    $showDebug = true; // Set to false to hide debug info
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Seminar Check-in System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../public/assets/css/style.css">
</head>
<body>
    <div class="container">
        <div class="login-form">
            <h2 class="text-center mb-4">Admin Login</h2>

            <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <?= htmlspecialchars($error) ?>
            </div>
            <?php endif; ?>

            <?php if (isset($showDebug) && $showDebug && isset($debug)): ?>
            <div class="alert alert-info">
                <h5>Debug Information:</h5>
                <?= $debug ?>
            </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Login</button>
                </div>
            </form>

            <div class="text-center mt-3">
                <a href="../public/index.php">Back to Public Site</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>