<?php
// Include database connection
require_once 'config/database.php';

// File path for the SQL export
$filename = 'database_export_' . date('Y-m-d_H-i-s') . '.sql';
$filepath = __DIR__ . '/' . $filename;

// Create file handle
$file = fopen($filepath, 'w');
if (!$file) {
    die("Unable to create export file. Check permissions.");
}

// Write header to file
fwrite($file, "-- Database Export\n");
fwrite($file, "-- Generated: " . date('Y-m-d H:i:s') . "\n");
fwrite($file, "-- Database: " . $dbname . "\n\n");
fwrite($file, "SET FOREIGN_KEY_CHECKS=0;\n\n");

// Get all tables in the database
$stmt = $pdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Process each table
foreach ($tables as $table) {
    // Get create table statement
    $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
    $createTable = $stmt->fetch(PDO::FETCH_ASSOC);
    $createTableSql = $createTable['Create Table'];

    // Write drop table and create table statements to file
    fwrite($file, "-- Table structure for table `$table`\n");
    fwrite($file, "DROP TABLE IF EXISTS `$table`;\n");
    fwrite($file, $createTableSql . ";\n\n");

    // Get table data
    $stmt = $pdo->query("SELECT * FROM `$table`");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($rows) > 0) {
        fwrite($file, "-- Data for table `$table`\n");

        // Get column names
        $columns = array_keys($rows[0]);
        $columnList = '`' . implode('`, `', $columns) . '`';

        // Start insert statement
        fwrite($file, "INSERT INTO `$table` ($columnList) VALUES\n");

        $rowCount = count($rows);
        foreach ($rows as $i => $row) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = $pdo->quote($value);
                }
            }

            $valueString = implode(', ', $values);
            fwrite($file, "($valueString)");

            // Add comma if not the last row
            if ($i < $rowCount - 1) {
                fwrite($file, ",\n");
            } else {
                fwrite($file, ";\n\n");
            }
        }
    }
}

// Write footer
fwrite($file, "SET FOREIGN_KEY_CHECKS=1;\n");

// Close the file
fclose($file);

// Output success message with link to download
echo "<html><head><title>Database Export</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;line-height:1.6}
.container{max-width:800px;margin:0 auto;padding:20px;border:1px solid #ddd;border-radius:5px}
.success{color:#28a745;font-weight:bold}
.btn{display:inline-block;padding:10px 15px;background:#007bff;color:#fff;text-decoration:none;border-radius:4px}
</style></head><body>";
echo "<div class='container'>";
echo "<h1>Database Export</h1>";
echo "<p class='success'>Database exported successfully!</p>";
echo "<p>The export file has been created: <strong>$filename</strong></p>";
echo "<p>File size: " . round(filesize($filepath) / 1024, 2) . " KB</p>";
echo "<p><a href='$filename' class='btn' download>Download SQL File</a></p>";
echo "</div></body></html>";
exit;
