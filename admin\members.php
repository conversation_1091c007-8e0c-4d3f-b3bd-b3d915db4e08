<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();

// Get event ID from query string
$eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;
$event = null;
$attendees = [];

if ($eventId) {
    // Get event details
    $event = getEventById($eventId);

    if ($event) {
        // Get attendees for this event
        $attendees = getAttendeesByEventId($eventId);
    }
}
?>

<div class="row mb-4">
    <div class="col-md-8">
        <h1>Manage Attendees</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Attendees</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="../public/upload_csv.php<?= $eventId ? "?event_id=$eventId" : '' ?>" class="btn btn-primary">
            <i class="bi bi-upload"></i> Upload CSV
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Event</h5>
                <select id="eventSelect" class="form-select">
                    <option value="">-- Select an event --</option>
                    <?php foreach ($events as $e): ?>
                        <option value="<?= $e['id'] ?>" <?= $eventId == $e['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($e['name']) ?>
                            (<?= date('d M Y', strtotime($e['start_date'])) ?> - <?= date('d M Y', strtotime($e['end_date'])) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>
</div>

<?php if ($event): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Attendees for: <?= htmlspecialchars($event['name']) ?></h5>
                <div>
                    <form action="../api/export_api.php" method="POST" class="d-inline quick-export-form">
                        <input type="hidden" name="event_id" value="<?= $eventId ?>">
                        <input type="hidden" name="format" value="excel">
                        <input type="hidden" name="type" value="attendees">
                        <input type="hidden" name="fields[]" value="number">
                        <input type="hidden" name="fields[]" value="name">
                        <input type="hidden" name="fields[]" value="company">
                        <input type="hidden" name="fields[]" value="phone">
                        <input type="hidden" name="fields[]" value="group">
                        <input type="hidden" name="fields[]" value="event_date">
                        <input type="hidden" name="fields[]" value="checkin_status">
                        <input type="hidden" name="fields[]" value="checkin_time">
                        <button type="submit" class="btn btn-sm btn-success me-2">
                            <i class="bi bi-file-excel"></i> Quick Export
                        </button>
                    </form>
                    <a href="../public/upload_csv.php?event_id=<?= $eventId ?>" class="btn btn-sm btn-primary me-2">
                        <i class="bi bi-upload"></i> Upload CSV
                    </a>
                    <a href="export.php?event_id=<?= $eventId ?>" class="btn btn-sm btn-secondary">
                        <i class="bi bi-download"></i> Advanced Export
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($attendees)): ?>
                <div class="alert alert-info">
                    No attendees found for this event. <a href="../public/upload_csv.php?event_id=<?= $eventId ?>" class="alert-link">Upload a CSV file</a> to add attendees.
                </div>
                <?php else: ?>
                <div class="search-box mb-4">
                    <i class="bi bi-search"></i>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search by name, company, or group...">
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Company</th>
                                <th>Group</th>
                                <th>Phone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($attendees as $attendee): ?>
                            <tr class="attendee-row">
                                <td><?= $attendee['number'] ?></td>
                                <td><?= htmlspecialchars($attendee['name']) ?></td>
                                <td><?= htmlspecialchars($attendee['company'] ?? '-') ?></td>
                                <td><?= htmlspecialchars($attendee['group'] ?? '-') ?></td>
                                <td><?= htmlspecialchars($attendee['phone'] ?? '-') ?></td>
                                <td>
                                    <a href="../public/checkin.php?event_id=<?= $eventId ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-box-arrow-in-right"></i> Check-in
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php elseif (count($events) > 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            Please select an event to view attendees.
        </div>
    </div>
</div>
<?php else: ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-warning">
            No events found. <a href="../public/event_create.php" class="alert-link">Create your first event</a>.
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Event selection change
document.getElementById('eventSelect').addEventListener('change', function() {
    if (this.value) {
        window.location.href = 'members.php?event_id=' + this.value;
    } else {
        window.location.href = 'members.php';
    }
});

// Search functionality
document.getElementById('searchInput')?.addEventListener('keyup', function() {
    const value = this.value.toLowerCase();
    const rows = document.querySelectorAll('.attendee-row');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(value) ? '' : 'none';
    });
});

// Handle quick export form
const quickExportForm = document.querySelector('.quick-export-form');
if (quickExportForm) {
    quickExportForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Create a hidden iframe for download if it doesn't exist
        let iframe = document.getElementById('downloadFrame');
        if (!iframe) {
            iframe = document.createElement('iframe');
            iframe.id = 'downloadFrame';
            iframe.name = 'downloadFrame';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
        }

        // Set the form target to the iframe
        this.target = 'downloadFrame';

        // Show a temporary success message
        const button = this.querySelector('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Exporting...';
        button.disabled = true;

        // Submit the form
        this.submit();

        // Reset the button after a delay
        setTimeout(function() {
            button.innerHTML = '<i class="bi bi-check"></i> Downloaded!';

            setTimeout(function() {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }, 1000);
    });
}
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>