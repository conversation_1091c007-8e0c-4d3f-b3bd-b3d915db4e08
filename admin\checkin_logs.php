<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();

// Get event ID from query string
$eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;
$date = isset($_GET['date']) ? $_GET['date'] : null;
$event = null;
$dates = [];
$checkinLogs = [];

if ($eventId) {
    // Get event details
    $event = getEventById($eventId);

    if ($event) {
        // Generate dates array
        $dates = generateDateRange($event['start_date'], $event['end_date']);

        // Get check-in logs for this event and date (if specified)
        $checkinLogs = getCheckinLogs($eventId, $date);
    }
}
?>

<div class="row mb-4">
    <div class="col-md-8">
        <h1>Check-in Logs</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Check-in Logs</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="export.php<?= $eventId ? "?event_id=$eventId" . ($date ? "&date=$date" : '') : '' ?>" class="btn btn-primary">
            <i class="bi bi-download"></i> Export
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Event</h5>
                <select id="eventSelect" class="form-select">
                    <option value="">-- Select an event --</option>
                    <?php foreach ($events as $e): ?>
                        <option value="<?= $e['id'] ?>" <?= $eventId == $e['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($e['name']) ?>
                            (<?= date('d M Y', strtotime($e['start_date'])) ?> - <?= date('d M Y', strtotime($e['end_date'])) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>

    <?php if ($event && !empty($dates)): ?>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Date (Optional)</h5>
                <select id="dateSelect" class="form-select">
                    <option value="">All Dates</option>
                    <?php foreach ($dates as $d): ?>
                        <option value="<?= $d ?>" <?= $date == $d ? 'selected' : '' ?>>
                            <?= date('d M Y (l)', strtotime($d)) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if ($event): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    Logs for: <?= htmlspecialchars($event['name']) ?>
                    <?= $date ? ' - ' . date('d M Y (l)', strtotime($date)) : '' ?>
                </h5>
                <div>
                    <form action="../api/export_api.php" method="POST" class="d-inline quick-export-form">
                        <input type="hidden" name="event_id" value="<?= $eventId ?>">
                        <?php if ($date): ?>
                        <input type="hidden" name="date" value="<?= $date ?>">
                        <?php endif; ?>
                        <input type="hidden" name="format" value="excel">
                        <input type="hidden" name="type" value="<?= $date ? 'attendees' : 'summary' ?>">
                        <input type="hidden" name="fields[]" value="number">
                        <input type="hidden" name="fields[]" value="name">
                        <input type="hidden" name="fields[]" value="department">
                        <input type="hidden" name="fields[]" value="event_date">
                        <input type="hidden" name="fields[]" value="checkin_status">
                        <input type="hidden" name="fields[]" value="checkin_time">
                        <button type="submit" class="btn btn-sm btn-success me-2">
                            <i class="bi bi-file-excel"></i> Quick Export
                        </button>
                    </form>
                    <a href="../public/checkin.php?event_id=<?= $eventId ?><?= $date ? "&date=$date" : '' ?>" class="btn btn-sm btn-primary me-2">
                        <i class="bi bi-box-arrow-in-right"></i> Go to Check-in
                    </a>
                    <a href="export.php?event_id=<?= $eventId ?><?= $date ? "&date=$date" : '' ?>" class="btn btn-sm btn-secondary">
                        <i class="bi bi-download"></i> Advanced Export
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($checkinLogs)): ?>
                <div class="alert alert-info">
                    No check-in logs found for this event<?= $date ? ' on this date' : '' ?>.
                </div>
                <?php else: ?>
                <div class="search-box mb-4">
                    <i class="bi bi-search"></i>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search by name, number, or department...">
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Date</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($checkinLogs as $log): ?>
                            <tr class="log-row">
                                <td><?= $log['number'] ?></td>
                                <td><?= htmlspecialchars($log['name']) ?></td>
                                <td><?= htmlspecialchars($log['department']) ?></td>
                                <td><?= date('d M Y (l)', strtotime($log['checkin_date'])) ?></td>
                                <td><?= date('H:i:s', strtotime($log['checkin_time'])) ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php elseif (count($events) > 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            Please select an event to view check-in logs.
        </div>
    </div>
</div>
<?php else: ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-warning">
            No events found. <a href="../public/event_create.php" class="alert-link">Create your first event</a>.
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Event selection change
document.getElementById('eventSelect').addEventListener('change', function() {
    if (this.value) {
        window.location.href = 'checkin_logs.php?event_id=' + this.value;
    } else {
        window.location.href = 'checkin_logs.php';
    }
});

// Date selection change
document.getElementById('dateSelect')?.addEventListener('change', function() {
    const eventId = document.getElementById('eventSelect').value;
    if (eventId) {
        if (this.value) {
            window.location.href = 'checkin_logs.php?event_id=' + eventId + '&date=' + this.value;
        } else {
            window.location.href = 'checkin_logs.php?event_id=' + eventId;
        }
    }
});

// Search functionality
document.getElementById('searchInput')?.addEventListener('keyup', function() {
    const value = this.value.toLowerCase();
    const rows = document.querySelectorAll('.log-row');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(value) ? '' : 'none';
    });
});

// Handle quick export form
const quickExportForm = document.querySelector('.quick-export-form');
if (quickExportForm) {
    quickExportForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Create a hidden iframe for download if it doesn't exist
        let iframe = document.getElementById('downloadFrame');
        if (!iframe) {
            iframe = document.createElement('iframe');
            iframe.id = 'downloadFrame';
            iframe.name = 'downloadFrame';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
        }

        // Set the form target to the iframe
        this.target = 'downloadFrame';

        // Show a temporary success message
        const button = this.querySelector('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> Exporting...';
        button.disabled = true;

        // Submit the form
        this.submit();

        // Reset the button after a delay
        setTimeout(function() {
            button.innerHTML = '<i class="bi bi-check"></i> Downloaded!';

            setTimeout(function() {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }, 1000);
    });
}
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>