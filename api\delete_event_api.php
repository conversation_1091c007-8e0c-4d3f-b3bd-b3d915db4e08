<?php
// Start session
session_start();

// Include functions
require_once '../config/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to perform this action.'
    ]);
    exit;
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
    exit;
}

// Check if event_id is provided
if (!isset($_POST['event_id']) || empty($_POST['event_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Event ID is required.'
    ]);
    exit;
}

// Get event ID
$eventId = (int)$_POST['event_id'];

// Get event details
$event = getEventById($eventId);

// Check if event exists
if (!$event) {
    echo json_encode([
        'success' => false,
        'message' => 'Event not found.'
    ]);
    exit;
}

try {
    global $pdo;
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Delete check-in logs associated with the event
    $stmt = $pdo->prepare("DELETE FROM checkin_logs WHERE event_id = ?");
    $stmt->execute([$eventId]);
    $checkinLogsDeleted = $stmt->rowCount();
    
    // Delete attendees associated with the event
    $stmt = $pdo->prepare("DELETE FROM attendees WHERE event_id = ?");
    $stmt->execute([$eventId]);
    $attendeesDeleted = $stmt->rowCount();
    
    // Delete the event
    $stmt = $pdo->prepare("DELETE FROM events WHERE id = ?");
    $stmt->execute([$eventId]);
    $eventDeleted = $stmt->rowCount();
    
    // Commit transaction
    $pdo->commit();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => "Event '{$event['name']}' has been deleted successfully. ($attendeesDeleted attendees and $checkinLogsDeleted check-in records were also removed.)"
    ]);
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => "Error deleting event: " . $e->getMessage()
    ]);
}
