// Main JavaScript file for the check-in system

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Search functionality
    $("#searchInput").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $(".attendee-card").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // Check-in functionality
    $(".btn-checkin").on("click", function() {
        var attendeeId = $(this).data("attendee-id");
        var eventId = $(this).data("event-id");
        var date = $(this).data("date");
        var card = $(this).closest(".attendee-card");
        
        $.ajax({
            url: "../api/checkin_api.php",
            type: "POST",
            data: {
                attendee_id: attendeeId,
                event_id: eventId,
                date: date,
                action: "checkin"
            },
            success: function(response) {
                var data = JSON.parse(response);
                if (data.success) {
                    // Update UI
                    card.find(".checkin-status")
                        .removeClass("not-checked-in")
                        .addClass("checked-in")
                        .text("Checked In");
                    
                    card.find(".btn-checkin")
                        .removeClass("btn-primary")
                        .addClass("btn-success")
                        .prop("disabled", true)
                        .text("✓ Done");
                    
                    card.removeClass("not-checked-in").addClass("checked-in");
                    
                    // Show success message
                    showAlert("success", data.message);
                } else {
                    // Show error message
                    showAlert("danger", data.message);
                }
            },
            error: function() {
                showAlert("danger", "An error occurred. Please try again.");
            }
        });
    });
    
    // Date picker for event creation
    if ($("#startDate").length && $("#endDate").length) {
        $("#startDate, #endDate").on("change", function() {
            validateDateRange();
        });
    }
    
    // File upload validation
    $("#csvFile").on("change", function() {
        var file = this.files[0];
        if (file) {
            var fileType = file.type || file.name.split('.').pop().toLowerCase();
            if (fileType !== 'text/csv' && fileType !== 'csv') {
                showAlert("danger", "Please select a CSV file.");
                $(this).val('');
            }
        }
    });
    
    // Event selection in check-in page
    $("#eventSelect").on("change", function() {
        if ($(this).val()) {
            window.location.href = "checkin.php?event_id=" + $(this).val();
        }
    });
    
    // Date selection in check-in page
    $("#dateSelect").on("change", function() {
        var eventId = $("#eventSelect").val();
        if (eventId && $(this).val()) {
            window.location.href = "checkin.php?event_id=" + eventId + "&date=" + $(this).val();
        }
    });
});

// Function to validate date range
function validateDateRange() {
    var startDate = $("#startDate").val();
    var endDate = $("#endDate").val();
    
    if (startDate && endDate) {
        if (new Date(startDate) > new Date(endDate)) {
            showAlert("danger", "End date cannot be before start date.");
            $("#endDate").val('');
        }
    }
}

// Function to show alert messages
function showAlert(type, message) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>';
    
    // Remove any existing alerts
    $(".alert").remove();
    
    // Add the new alert at the top of the container
    $(".container").prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $(".alert").alert('close');
    }, 5000);
}
