<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="display-4">Welcome to Seminar Check-in System</h1>
        <p class="lead">Manage your seminar attendees and track check-ins efficiently.</p>
    </div>
</div>

<!-- <div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Check-in</h5>
                <p class="card-text">Record attendee check-ins for your seminar events.</p>
                <a href="checkin.php" class="btn btn-primary">Go to Check-in</a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Admin Panel</h5>
                <p class="card-text">Manage events, attendees, and view check-in logs.</p>
                <a href="../admin/index.php" class="btn btn-secondary">Go to Admin Panel</a>
            </div>
        </div>
    </div>
</div> -->

<div class="row mb-4">
    <div class="col-md-12">
        <h2>Recent Events</h2>
    </div>
</div>

<div class="row">
    <?php if (count($events) > 0): ?>
        <?php foreach ($events as $event): ?>
            <div class="col-md-6 mb-4">
                <div class="card event-card">
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($event['name']) ?></h5>
                        <p class="card-text event-dates">
                            <i class="bi bi-calendar"></i>
                            <?= date('d M Y', strtotime($event['start_date'])) ?> -
                            <?= date('d M Y', strtotime($event['end_date'])) ?>
                        </p>
                        <p class="card-text"><?= htmlspecialchars($event['description']) ?></p>

                        <?php
                        // Get check-in statistics for this event
                        $stats = getEventCheckInStats($event['id']);
                        if (!empty($stats)):
                        ?>
                        <div class="event-stats mb-3">
                            <h6>Check-in Statistics:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Date</th>
                                            <th>Checked-in</th>
                                            <th>Not Checked-in</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($stats as $date => $stat): ?>
                                        <tr>
                                            <td><?= date('d M (D)', strtotime($date)) ?></td>
                                            <td>
                                                <span class="badge bg-success"><?= $stat['checked_in'] ?></span>
                                                <small class="text-muted"><?= $stat['percentage'] ?>%</small>
                                            </td>
                                            <td><span class="badge bg-secondary"><?= $stat['not_checked_in'] ?></span></td>
                                            <td><span class="badge bg-primary"><?= $stat['total'] ?></span></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <?php endif; ?>

                        <a href="checkin.php?event_id=<?= $event['id'] ?>" class="btn btn-primary">Check-in</a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="col-md-12">
            <div class="alert alert-info">
                <?php if (isAdmin()): ?>
                No events found. <a href="event_create.php" class="alert-link">Create your first event</a>.
                <?php else: ?>
                No events found. Please contact an administrator to create events.
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>