<?php
// Start session
session_start();

// Include functions
require_once '../config/functions.php';

// Handle GET requests (for getting dates)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_dates') {
    $eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;

    if (!$eventId) {
        echo json_encode(['success' => false, 'message' => 'Event ID is required.']);
        exit;
    }

    $event = getEventById($eventId);

    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found.']);
        exit;
    }

    // Generate dates array
    $datesArray = generateDateRange($event['start_date'], $event['end_date']);
    $formattedDates = [];

    foreach ($datesArray as $date) {
        $formattedDates[] = [
            'value' => $date,
            'label' => date('d M Y (l)', strtotime($date))
        ];
    }

    echo json_encode(['success' => true, 'dates' => $formattedDates]);
    exit;
}

// Handle POST requests (for creating events)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if user is admin
    if (!isAdmin()) {
        $_SESSION['message'] = "You don't have permission to create events. Only administrators can create events.";
        $_SESSION['message_type'] = "danger";
        redirect('../public/index.php');
    }
    // Get form data
    $name = sanitize($_POST['name'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $startDate = $_POST['start_date'] ?? '';
    $endDate = $_POST['end_date'] ?? '';

    // Validate input
    $errors = [];

    if (empty($name)) {
        $errors[] = "Event name is required.";
    }

    if (empty($startDate)) {
        $errors[] = "Start date is required.";
    }

    if (empty($endDate)) {
        $errors[] = "End date is required.";
    }

    if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate)) {
        $errors[] = "End date cannot be before start date.";
    }

    // If there are errors, redirect back with error message
    if (!empty($errors)) {
        $_SESSION['message'] = implode("<br>", $errors);
        $_SESSION['message_type'] = "danger";
        redirect('../public/event_create.php');
    }

    try {
        // Insert event into database
        global $pdo;
        $stmt = $pdo->prepare("INSERT INTO events (name, description, start_date, end_date) VALUES (?, ?, ?, ?)");
        $stmt->execute([$name, $description, $startDate, $endDate]);

        $eventId = $pdo->lastInsertId();

        // Set success message and redirect
        $_SESSION['message'] = "Event created successfully.";
        $_SESSION['message_type'] = "success";
        redirect('../public/event_list.php');
    } catch (PDOException $e) {
        // Set error message and redirect
        $_SESSION['message'] = "Error creating event: " . $e->getMessage();
        $_SESSION['message_type'] = "danger";
        redirect('../public/event_create.php');
    }
} else {
    // If not a POST or GET request, redirect to event list
    redirect('../public/event_list.php');
}