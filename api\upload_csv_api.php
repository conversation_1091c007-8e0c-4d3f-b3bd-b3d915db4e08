<?php
// Start session
session_start();

// Include functions
require_once '../config/functions.php';

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error_message'] = "Invalid request method.";
    redirect('../public/error.php');
}

// Check if event_id is provided
if (!isset($_POST['event_id']) || empty($_POST['event_id'])) {
    $_SESSION['error_message'] = "Event ID is required.";
    $_SESSION['return_url'] = '../public/event_list.php';
    redirect('../public/error.php');
}

// Get event details
$eventId = (int)$_POST['event_id'];
$event = getEventById($eventId);

// Check if event exists
if (!$event) {
    $_SESSION['error_message'] = "Event not found.";
    $_SESSION['return_url'] = '../public/event_list.php';
    redirect('../public/error.php');
}

// Check if file was uploaded
if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
    $_SESSION['error_message'] = "Error uploading file. Please try again.";
    $_SESSION['return_url'] = "../public/upload_csv.php?event_id=$eventId";
    redirect('../public/error.php');
}

// Check file type
$fileType = pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION);
if ($fileType !== 'csv') {
    $_SESSION['error_message'] = "Only CSV files are allowed.";
    $_SESSION['return_url'] = "../public/upload_csv.php?event_id=$eventId";
    redirect('../public/error.php');
}

// Open the file
$file = fopen($_FILES['csv_file']['tmp_name'], 'r');
if (!$file) {
    $_SESSION['error_message'] = "Error opening file.";
    $_SESSION['return_url'] = "../public/upload_csv.php?event_id=$eventId";
    redirect('../public/error.php');
}

// For this CSV format, we'll assume the columns are in this order: No, Name, Company, Phone, Group
// No header row in the file, so we'll define the column indexes manually
$columnIndexes = [
    'No' => 0,      // First column is the number
    'Name' => 1,    // Second column is the name
    'Company' => 2, // Third column is the company
    'Phone' => 3,   // Fourth column is the phone (optional)
    'Group' => 4,   // Fifth column is the group (optional)
];

// Check if we need to skip the first row (header)
$skipFirstRow = isset($_POST['skip_first_row']) && $_POST['skip_first_row'] === 'on';

// Rewind the file pointer to the beginning
rewind($file);

// Skip the first row if requested
if ($skipFirstRow) {
    fgetcsv($file);
}

try {
    // Begin transaction
    global $pdo;
    $pdo->beginTransaction();

    // Check if we need to delete existing attendees
    if (isset($_POST['delete_existing']) && $_POST['delete_existing'] === 'on') {
        $deleteStmt = $pdo->prepare("DELETE FROM attendees WHERE event_id = ?");
        $deleteStmt->execute([$eventId]);

        // Get the number of deleted rows
        $deletedCount = $deleteStmt->rowCount();
    }

    // Debug the SQL statement
    error_log("SQL Statement: INSERT INTO attendees (event_id, number, name, company, phone, `group`) VALUES (?, ?, ?, ?, ?, ?)");

    // Prepare statement for inserting attendees
    $stmt = $pdo->prepare("INSERT INTO attendees (event_id, number, name, company, phone, `group`) VALUES (?, ?, ?, ?, ?, ?)");

    // Counter for successful inserts
    $insertCount = 0;

    // Process each row
    while (($row = fgetcsv($file)) !== false) {
        // Skip empty rows
        if (empty($row) || count($row) < 3) { // We need at least No, Name, Company
            error_log("Skipping row - not enough columns: " . implode(',', $row));
            continue;
        }

        // Debug information
        error_log("Processing row: " . implode(',', $row));

        // Get data from row
        $number = (int)$row[$columnIndexes['No']]; // Convert to integer
        $name = $row[$columnIndexes['Name']];
        $company = $row[$columnIndexes['Company']];

        // Optional fields
        $phone = isset($row[$columnIndexes['Phone']]) ? $row[$columnIndexes['Phone']] : null;
        $group = isset($row[$columnIndexes['Group']]) ? $row[$columnIndexes['Group']] : null;

        // Debug information
        error_log("Extracted data: number=$number, name=$name, company=$company, phone=$phone, group=$group");

        // Skip rows with empty required fields
        if (empty($name) || empty($company)) {
            error_log("Skipping row due to empty name or company");
            continue;
        }

        // Debug the values being inserted
        error_log("Inserting values: eventId=$eventId, number=$number, name=$name, company=$company, phone=$phone, group=$group");

        try {
            // Insert into database - make sure the order matches the SQL statement
            // SQL: INSERT INTO attendees (event_id, number, name, company, phone, `group`)
            $params = [
                $eventId, // event_id
                $number,  // number
                $name,    // name
                $company, // company
                $phone,   // phone
                $group    // group
            ];

            // Log the parameters in order
            error_log("Parameters in order: " . json_encode($params));

            $stmt->execute($params);
            $insertCount++;
            error_log("Row inserted successfully");
        } catch (PDOException $e) {
            error_log("Error inserting row: " . $e->getMessage());
            throw $e; // Re-throw to be caught by the outer try-catch
        }
    }

    // Commit transaction
    $pdo->commit();

    // Close the file
    fclose($file);

    // Set success message and redirect
    $successMessage = "$insertCount attendees imported successfully.";

    // Add information about deleted records if applicable
    if (isset($deletedCount) && $deletedCount > 0) {
        $successMessage .= " $deletedCount existing attendees were removed.";
    }

    // Add information about skipping the first row if applicable
    if ($skipFirstRow) {
        $successMessage .= " First row was skipped.";
    }

    // Check if coming from admin panel
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    $isAdmin = strpos($referer, '/admin/') !== false;

    $_SESSION['success_message'] = $successMessage;

    if ($isAdmin) {
        $_SESSION['return_url'] = "../admin/index.php";
        $_SESSION['return_text'] = "Back to Dashboard";
    } else {
        $_SESSION['return_url'] = "../admin/members.php?event_id=$eventId";
        $_SESSION['return_text'] = "View Attendees";
    }

    redirect('../public/success.php');

} catch (PDOException $e) {
    // Rollback transaction on error
    $pdo->rollBack();

    // Close the file
    fclose($file);

    // Check if coming from admin panel
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    $isAdmin = strpos($referer, '/admin/') !== false;

    // Set error message and redirect
    $_SESSION['error_message'] = "Error importing attendees: " . $e->getMessage();

    if ($isAdmin) {
        $_SESSION['return_url'] = "../admin/index.php";
    } else {
        $_SESSION['return_url'] = "../public/upload_csv.php?event_id=$eventId";
    }

    redirect('../public/error.php');
}