<?php
// Include initialization file
require_once 'config/init.php';

// Display timezone information
echo "<h1>Timezone Information</h1>";
echo "<p>Current PHP timezone setting: " . date_default_timezone_get() . "</p>";
echo "<p>Current date and time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Current timestamp: " . time() . "</p>";

// Display formatted date examples
echo "<h2>Formatted Date Examples</h2>";
echo "<p>Full date and time: " . date('l, F j, Y g:i:s A') . "</p>";
echo "<p>Short date: " . date('d/m/Y') . "</p>";
echo "<p>Thai date format: " . date('d M Y', strtotime('now')) . "</p>";

// Display server information
echo "<h2>Server Information</h2>";
echo "<p>Server time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>UTC time: " . gmdate('Y-m-d H:i:s') . "</p>";
echo "<p>Time difference from UTC: " . (date('Z') / 3600) . " hours</p>";

// Display all available timezones
echo "<h2>All Available Timezones</h2>";
echo "<p>Total timezones: " . count(DateTimeZone::listIdentifiers()) . "</p>";

// Display Asia timezones
echo "<h3>Asia Timezones</h3>";
echo "<ul>";
$timezones = DateTimeZone::listIdentifiers(DateTimeZone::ASIA);
foreach ($timezones as $timezone) {
    $now = new DateTime('now', new DateTimeZone($timezone));
    echo "<li>$timezone: " . $now->format('Y-m-d H:i:s') . "</li>";
}
echo "</ul>";
?>
