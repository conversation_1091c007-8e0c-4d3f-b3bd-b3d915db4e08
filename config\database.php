<?php
// Database configuration local
// $host = 'localhost';
// $dbname = 'checkin_system';
// $username = 'root';
// $password = 'kokoku123#@!';

// Database configuration server
$host = 'localhost';
$dbname = 'u506880141_checkinapp';
$username = 'u506880141_checkinapp';
$password = 'FdFdhk0kopiko123#@!';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}