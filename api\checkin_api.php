<?php
// Include initialization file
require_once '../config/init.php';

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Check if required parameters are provided
if (!isset($_POST['attendee_id']) || !isset($_POST['event_id']) || !isset($_POST['date']) || !isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters.']);
    exit;
}

// Get parameters
$attendeeId = (int)$_POST['attendee_id'];
$eventId = (int)$_POST['event_id'];
$date = $_POST['date'];
$action = $_POST['action'];

// Debug log
error_log("Checkin API called with: attendee_id=$attendeeId, event_id=$eventId, date=$date, action=$action");

// Validate action
if ($action !== 'checkin' && $action !== 'checkout') {
    echo json_encode(['success' => false, 'message' => 'Invalid action.']);
    exit;
}

// Get attendee details
$attendee = getAttendeeById($attendeeId);

// Check if attendee exists
if (!$attendee) {
    echo json_encode(['success' => false, 'message' => 'Attendee not found.']);
    exit;
}

// Get event details
$event = getEventById($eventId);

// Check if event exists
if (!$event) {
    echo json_encode(['success' => false, 'message' => 'Event not found.']);
    exit;
}

// Check if date is valid
$dates = generateDateRange($event['start_date'], $event['end_date']);
if (!in_array($date, $dates)) {
    echo json_encode(['success' => false, 'message' => 'Invalid date for this event.']);
    exit;
}

// Check if already checked in
$isCheckedIn = isCheckedIn($attendeeId, $eventId, $date);
$currentTime = date('H:i:s');

try {
    global $pdo;

    if ($action === 'checkin') {
        if ($isCheckedIn) {
            // Already checked in, return the check-in time
            $stmt = $pdo->prepare("SELECT checkin_time FROM checkin_logs WHERE attendee_id = ? AND event_id = ? AND checkin_date = ?");
            $stmt->execute([$attendeeId, $eventId, $date]);
            $checkinTime = $stmt->fetchColumn();

            echo json_encode([
                'success' => true,
                'message' => 'Already checked in.',
                'attendee' => [
                    'id' => $attendee['id'],
                    'name' => $attendee['name'],
                    'number' => $attendee['number'],
                    'department' => $attendee['department']
                ],
                'checkin_time' => $checkinTime
            ]);
            exit;
        }

        // Insert check-in record
        $stmt = $pdo->prepare("INSERT INTO checkin_logs (attendee_id, event_id, checkin_date, checkin_time) VALUES (?, ?, ?, ?)");
        $stmt->execute([$attendeeId, $eventId, $date, $currentTime]);

        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Check-in successful.',
            'attendee' => [
                'id' => $attendee['id'],
                'name' => $attendee['name'],
                'number' => $attendee['number'],
                'department' => $attendee['department']
            ],
            'checkin_time' => $currentTime
        ]);
    } else if ($action === 'checkout') {
        error_log("Processing checkout action for attendee $attendeeId");

        if (!$isCheckedIn) {
            error_log("Error: Attendee $attendeeId is not checked in for date $date");
            echo json_encode(['success' => false, 'message' => 'Attendee is not checked in for this date.']);
            exit;
        }

        // Delete check-in record
        $stmt = $pdo->prepare("DELETE FROM checkin_logs WHERE attendee_id = ? AND event_id = ? AND checkin_date = ?");
        $stmt->execute([$attendeeId, $eventId, $date]);
        $rowsAffected = $stmt->rowCount();
        error_log("Deleted $rowsAffected check-in records for attendee $attendeeId");

        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Check-out successful.',
            'attendee' => [
                'id' => $attendee['id'],
                'name' => $attendee['name'],
                'number' => $attendee['number'],
                'department' => $attendee['department']
            ]
        ]);
    }
} catch (PDOException $e) {
    // Return error response
    echo json_encode(['success' => false, 'message' => 'Error processing request: ' . $e->getMessage()]);
}