<?php
// Include header
require_once 'includes/header.php';

// Check if event_id is provided
if (!isset($_GET['event_id']) || empty($_GET['event_id'])) {
    $_SESSION['message'] = "Event ID is required.";
    $_SESSION['message_type'] = "danger";

    // Check if coming from admin panel
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    if (strpos($referer, '/admin/') !== false) {
        redirect('../admin/index.php');
    } else {
        redirect('event_list.php');
    }
}

// Get event details
$eventId = (int)$_GET['event_id'];
$event = getEventById($eventId);

// Check if event exists
if (!$event) {
    $_SESSION['message'] = "Event not found.";
    $_SESSION['message_type'] = "danger";

    // Check if coming from admin panel
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    if (strpos($referer, '/admin/') !== false) {
        redirect('../admin/index.php');
    } else {
        redirect('event_list.php');
    }
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Upload Attendees CSV</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php
                // Check if coming from admin panel
                $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
                $isAdmin = strpos($referer, '/admin/') !== false;

                if ($isAdmin): ?>
                <li class="breadcrumb-item"><a href="../admin/index.php">Admin Dashboard</a></li>
                <?php else: ?>
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item"><a href="event_list.php">Events</a></li>
                <?php endif; ?>
                <li class="breadcrumb-item active" aria-current="page">Upload CSV</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Upload for: <?= htmlspecialchars($event['name']) ?></h5>
                <p class="card-text event-dates">
                    <i class="bi bi-calendar"></i>
                    <?= date('d M Y', strtotime($event['start_date'])) ?> -
                    <?= date('d M Y', strtotime($event['end_date'])) ?>
                </p>

                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> CSV Format Requirements:</h6>
                    <ul>
                        <li>File must be in CSV format</li>
                        <li><strong>Primary fields (required):</strong> No, Name, Company</li>
                        <li><strong>Optional fields:</strong> Phone, Group</li>
                        <li>CSV should have columns in this order: No, Name, Company, Phone, Group</li>
                        <li>No header row is needed - the file should start directly with data</li>
                        <li>Example: 1,John Smith,Acme Corporation,0812345678,IT Department</li>
                        <li>UTF-8 encoding is recommended</li>
                    </ul>
                </div>

                <form action="../api/upload_csv_api.php" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="event_id" value="<?= $eventId ?>">

                    <div class="mb-3">
                        <label for="csvFile" class="form-label">Select CSV File *</label>
                        <input class="form-control" type="file" id="csvFile" name="csv_file" accept=".csv" required>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="deleteExisting" name="delete_existing">
                        <label class="form-check-label" for="deleteExisting">Delete previous data in this event</label>
                        <div class="form-text">Check this box to remove all existing attendees before uploading new ones.</div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="skipFirstRow" name="skip_first_row">
                        <label class="form-check-label" for="skipFirstRow">Do not save the first record</label>
                        <div class="form-text">Check this box if your CSV file contains a header row that should be skipped.</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <?php if ($isAdmin): ?>
                        <a href="../admin/index.php" class="btn btn-secondary me-md-2">Cancel</a>
                        <?php else: ?>
                        <a href="event_list.php" class="btn btn-secondary me-md-2">Cancel</a>
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">Upload</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>