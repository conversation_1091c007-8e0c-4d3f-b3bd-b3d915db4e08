# Seminar Check-in System

A web-based system for managing seminar attendees and tracking check-ins.

## Features

- Create events with date ranges
- Upload attendee lists via CSV
- Check-in attendees for specific dates
- Export data to CSV or Excel
- Admin dashboard with statistics
- Search functionality

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)

## Installation

1. Clone or download this repository to your web server directory
2. Create a MySQL database named `checkin_system`
3. Import the `database_setup.sql` file to create the necessary tables
4. Configure the database connection in `config/database.php`
5. Make sure the web server has write permissions to the directory

## Database Setup

Run the SQL script in `database_setup.sql` to create the database and tables:

```sql
-- Create database if not exists
CREATE DATABASE IF NOT EXISTS checkin_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE checkin_system;

-- Create tables...
```

## Configuration

Edit the `config/database.php` file to match your database credentials:

```php
$host = 'localhost';
$dbname = 'checkin_system';
$username = 'your_username';
$password = 'your_password';
```

## Usage

### Admin Access

- URL: `/admin/`
- Default username: `admin`
- Default password: `admin123`

### Creating an Event

1. Log in to the admin panel
2. Click on "Create Event"
3. Fill in the event details (name, description, date range)
4. Submit the form

### Uploading Attendees

1. Go to the event list
2. Click on "Upload CSV" for the desired event
3. Select a CSV file with the required columns
4. Submit the form

### Check-in Process

1. Go to the check-in page
2. Select an event and date
3. Search for an attendee by name, number, or department
4. Click the "Check-in" button for the attendee

### Exporting Data

1. Go to the export page
2. Select an event and optionally a specific date
3. Choose the export format (CSV or Excel)
4. Choose the export type (all attendees, checked-in only, or summary)
5. Click "Export"

## CSV Format

The CSV file for uploading attendees should have the following columns:

- `ลำดับ` (Number)
- `รายชื่อ` (Name)
- `ส่วนงาน` (Department)
- `Email` (Optional)
- `Phone` (Optional)

Example:
```
ลำดับ,รายชื่อ,ส่วนงาน,Email,Phone
1,John Doe,IT Department,<EMAIL>,************
2,Jane Smith,HR Department,<EMAIL>,************
```

## License

This project is licensed under the MIT License.

## Credits

Developed by [Your Name]
