<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();

// Check if event_id is provided
$eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;
$event = null;
$attendees = [];
$dates = [];
$selectedDate = null;

if ($eventId) {
    // Get event details
    $event = getEventById($eventId);
    
    if ($event) {
        // Generate dates array
        $dates = generateDateRange($event['start_date'], $event['end_date']);
        
        // Get selected date or use current date if within range
        $today = date('Y-m-d');
        $selectedDate = isset($_GET['date']) ? $_GET['date'] : null;
        
        // If no date selected or selected date not in range, use today if in range, otherwise use first date
        if (!$selectedDate || !in_array($selectedDate, $dates)) {
            if (in_array($today, $dates)) {
                $selectedDate = $today;
            } else {
                $selectedDate = $dates[0] ?? null;
            }
        }
        
        // Get attendees for this event
        $attendees = getAttendeesByEventId($eventId);
    }
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Admin Check-in</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Check-in</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Event</h5>
                <select id="eventSelect" class="form-select">
                    <option value="">-- Select an event --</option>
                    <?php foreach ($events as $e): ?>
                        <option value="<?= $e['id'] ?>" <?= $eventId == $e['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($e['name']) ?> 
                            (<?= date('d M Y', strtotime($e['start_date'])) ?> - <?= date('d M Y', strtotime($e['end_date'])) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>
    
    <?php if ($event && !empty($dates)): ?>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Date</h5>
                <select id="dateSelect" class="form-select">
                    <?php foreach ($dates as $date): ?>
                        <option value="<?= $date ?>" <?= $selectedDate == $date ? 'selected' : '' ?>>
                            <?= date('d M Y (l)', strtotime($date)) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if ($event && $selectedDate): ?>
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <?= htmlspecialchars($event['name']) ?> - 
                    <?= date('d M Y (l)', strtotime($selectedDate)) ?>
                </h5>
                
                <div class="search-box">
                    <i class="bi bi-search"></i>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search by name, number, or department...">
                </div>
                
                <?php if (empty($attendees)): ?>
                <div class="alert alert-warning">
                    No attendees found for this event. <a href="../public/upload_csv.php?event_id=<?= $eventId ?>" class="alert-link">Upload a CSV file</a> to add attendees.
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($attendees as $attendee): ?>
                        <?php 
                        $isCheckedIn = isCheckedIn($attendee['id'], $eventId, $selectedDate);
                        $cardClass = $isCheckedIn ? 'checked-in' : 'not-checked-in';
                        $buttonClass = $isCheckedIn ? 'btn-success' : 'btn-primary';
                        $buttonText = $isCheckedIn ? '✓ Done' : 'Check-in';
                        $buttonDisabled = $isCheckedIn ? 'disabled' : '';
                        ?>
                        <div class="col-md-6 mb-3 attendee-card">
                            <div class="card checkin-card <?= $cardClass ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="card-title">
                                                <span class="attendee-number">#<?= $attendee['number'] ?></span> 
                                                <?= htmlspecialchars($attendee['name']) ?>
                                            </h5>
                                            <p class="card-text"><?= htmlspecialchars($attendee['department']) ?></p>
                                        </div>
                                        <div class="text-end">
                                            <p class="checkin-status <?= $cardClass ?>">
                                                <?= $isCheckedIn ? 'Checked In' : 'Not Checked In' ?>
                                            </p>
                                            <button 
                                                class="btn <?= $buttonClass ?> btn-checkin" 
                                                data-attendee-id="<?= $attendee['id'] ?>" 
                                                data-event-id="<?= $eventId ?>" 
                                                data-date="<?= $selectedDate ?>"
                                                <?= $buttonDisabled ?>
                                            >
                                                <?= $buttonText ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php elseif (!$event && count($events) > 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            Please select an event to proceed with check-in.
        </div>
    </div>
</div>
<?php elseif (count($events) == 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-warning">
            No events found. <a href="../public/event_create.php" class="alert-link">Create your first event</a>.
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Event selection change
document.getElementById('eventSelect').addEventListener('change', function() {
    if (this.value) {
        window.location.href = 'checkin.php?event_id=' + this.value;
    } else {
        window.location.href = 'checkin.php';
    }
});

// Date selection change
document.getElementById('dateSelect')?.addEventListener('change', function() {
    const eventId = document.getElementById('eventSelect').value;
    if (eventId) {
        window.location.href = 'checkin.php?event_id=' + eventId + '&date=' + this.value;
    }
});

// Search functionality
document.getElementById('searchInput')?.addEventListener('keyup', function() {
    var value = $(this).val().toLowerCase();
    $(".attendee-card").filter(function() {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
    });
});

// Check-in functionality
$(".btn-checkin").on("click", function() {
    var attendeeId = $(this).data("attendee-id");
    var eventId = $(this).data("event-id");
    var date = $(this).data("date");
    var card = $(this).closest(".attendee-card");
    
    $.ajax({
        url: "../api/checkin_api.php",
        type: "POST",
        data: {
            attendee_id: attendeeId,
            event_id: eventId,
            date: date,
            action: "checkin"
        },
        success: function(response) {
            var data = JSON.parse(response);
            if (data.success) {
                // Update UI
                card.find(".checkin-status")
                    .removeClass("not-checked-in")
                    .addClass("checked-in")
                    .text("Checked In");
                
                card.find(".btn-checkin")
                    .removeClass("btn-primary")
                    .addClass("btn-success")
                    .prop("disabled", true)
                    .text("✓ Done");
                
                card.find(".checkin-card")
                    .removeClass("not-checked-in")
                    .addClass("checked-in");
                
                // Show success message
                alert("Check-in successful!");
            } else {
                // Show error message
                alert("Error: " + data.message);
            }
        },
        error: function() {
            alert("An error occurred. Please try again.");
        }
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
