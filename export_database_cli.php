<?php
// This script is designed to be run from the command line
// Usage: php export_database_cli.php

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script can only be run from the command line.");
}

// Include database connection
require_once 'config/database.php';

// File path for the SQL export
$filename = 'database_export_' . date('Y-m-d_H-i-s') . '.sql';
$filepath = __DIR__ . '/' . $filename;

echo "Starting database export...\n";
echo "Database: $dbname\n";
echo "Output file: $filepath\n\n";

// Create file handle
$file = fopen($filepath, 'w');
if (!$file) {
    die("Error: Unable to create export file. Check permissions.\n");
}

// Write header to file
fwrite($file, "-- Database Export\n");
fwrite($file, "-- Generated: " . date('Y-m-d H:i:s') . "\n");
fwrite($file, "-- Database: " . $dbname . "\n\n");
fwrite($file, "SET FOREIGN_KEY_CHECKS=0;\n\n");

// Get all tables in the database
$stmt = $pdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "Found " . count($tables) . " tables.\n";

// Process each table
foreach ($tables as $table) {
    echo "Exporting table: $table\n";
    
    // Get create table statement
    $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
    $createTable = $stmt->fetch(PDO::FETCH_ASSOC);
    $createTableSql = $createTable['Create Table'];
    
    // Write drop table and create table statements to file
    fwrite($file, "-- Table structure for table `$table`\n");
    fwrite($file, "DROP TABLE IF EXISTS `$table`;\n");
    fwrite($file, $createTableSql . ";\n\n");
    
    // Get table data
    $stmt = $pdo->query("SELECT * FROM `$table`");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "  - Exporting " . count($rows) . " rows\n";
    
    if (count($rows) > 0) {
        fwrite($file, "-- Data for table `$table`\n");
        
        // Get column names
        $columns = array_keys($rows[0]);
        $columnList = '`' . implode('`, `', $columns) . '`';
        
        // Start insert statement
        fwrite($file, "INSERT INTO `$table` ($columnList) VALUES\n");
        
        $rowCount = count($rows);
        foreach ($rows as $i => $row) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = $pdo->quote($value);
                }
            }
            
            $valueString = implode(', ', $values);
            fwrite($file, "($valueString)");
            
            // Add comma if not the last row
            if ($i < $rowCount - 1) {
                fwrite($file, ",\n");
            } else {
                fwrite($file, ";\n\n");
            }
        }
    }
}

// Write footer
fwrite($file, "SET FOREIGN_KEY_CHECKS=1;\n");

// Close the file
fclose($file);

echo "\nDatabase export completed successfully!\n";
echo "File saved to: $filepath\n";
echo "File size: " . round(filesize($filepath) / 1024, 2) . " KB\n";
