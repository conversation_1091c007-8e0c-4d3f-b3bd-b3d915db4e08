<?php
// This file contains utility functions for the application
// Database connection is already included in init.php

/**
 * Sanitize input data
 *
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Redirect to a specific URL
 *
 * @param string $url URL to redirect to
 * @return void
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * Check if user is logged in
 *
 * @return bool True if logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user is admin
 *
 * @return bool True if admin, false otherwise
 */
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Get all events
 *
 * @return array Array of events
 */
function getAllEvents() {
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM events ORDER BY start_date DESC");
    return $stmt->fetchAll();
}

/**
 * Get event by ID
 *
 * @param int $id Event ID
 * @return array|bool Event data or false if not found
 */
function getEventById($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

/**
 * Get attendees by event ID
 *
 * @param int $eventId Event ID
 * @return array Array of attendees
 */
function getAttendeesByEventId($eventId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM attendees WHERE event_id = ? ORDER BY number ASC");
    $stmt->execute([$eventId]);
    return $stmt->fetchAll();
}

/**
 * Get attendee by ID
 *
 * @param int $id Attendee ID
 * @return array|bool Attendee data or false if not found
 */
function getAttendeeById($id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM attendees WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

/**
 * Check if attendee is checked in for a specific date
 *
 * @param int $attendeeId Attendee ID
 * @param int $eventId Event ID
 * @param string $date Date in Y-m-d format
 * @return bool True if checked in, false otherwise
 */
function isCheckedIn($attendeeId, $eventId, $date) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM checkin_logs WHERE attendee_id = ? AND event_id = ? AND checkin_date = ?");
    $stmt->execute([$attendeeId, $eventId, $date]);
    return $stmt->fetchColumn() > 0;
}

/**
 * Get check-in logs for a specific event and date
 *
 * @param int $eventId Event ID
 * @param string $date Date in Y-m-d format (optional)
 * @return array Array of check-in logs
 */
function getCheckinLogs($eventId, $date = null) {
    global $pdo;

    $sql = "SELECT cl.*, a.name, a.number, a.department
            FROM checkin_logs cl
            JOIN attendees a ON cl.attendee_id = a.id
            WHERE cl.event_id = ?";
    $params = [$eventId];

    if ($date) {
        $sql .= " AND cl.checkin_date = ?";
        $params[] = $date;
    }

    $sql .= " ORDER BY cl.checkin_date DESC, cl.checkin_time DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Generate dates array between start and end date
 *
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return array Array of dates
 */
function generateDateRange($startDate, $endDate) {
    $dates = [];
    $current = strtotime($startDate);
    $end = strtotime($endDate);

    while ($current <= $end) {
        $dates[] = date('Y-m-d', $current);
        $current = strtotime('+1 day', $current);
    }

    return $dates;
}

/**
 * Export data to CSV
 *
 * @param array $data Data to export
 * @param array $headers CSV headers
 * @param string $filename Filename without extension
 * @return void
 */
function exportToCSV($data, $headers, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');

    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Add headers
    fputcsv($output, $headers);

    // Add data
    foreach ($data as $row) {
        fputcsv($output, $row);
    }

    fclose($output);
    exit;
}

/**
 * Export data to Excel (XLSX)
 *
 * @param array $data Data to export
 * @param array $headers Excel headers
 * @param string $filename Filename without extension
 * @return void
 */
function exportToExcel($data, $headers, $filename) {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // Fallback to CSV if data is empty
    if (empty($data)) {
        exportToCSV($data, $headers, $filename);
        exit;
    }

    // Start output buffering
    ob_start();

    // Create Excel XML header
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";
    echo '<Worksheet ss:Name="Sheet1">' . "\n";
    echo '<Table>' . "\n";

    // Add headers row
    echo '<Row>' . "\n";
    foreach ($headers as $header) {
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($header) . '</Data></Cell>' . "\n";
    }
    echo '</Row>' . "\n";

    // Add data rows
    foreach ($data as $row) {
        echo '<Row>' . "\n";
        foreach ($row as $cell) {
            // Determine if the cell is numeric or string
            if (is_numeric($cell) && !str_contains($cell, '%')) {
                echo '<Cell><Data ss:Type="Number">' . $cell . '</Data></Cell>' . "\n";
            } else {
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars($cell) . '</Data></Cell>' . "\n";
            }
        }
        echo '</Row>' . "\n";
    }

    // Close XML tags
    echo '</Table>' . "\n";
    echo '</Worksheet>' . "\n";
    echo '</Workbook>' . "\n";

    // Send the output buffer
    ob_end_flush();
    exit;
}

/**
 * Get check-in statistics for an event
 *
 * @param int $eventId Event ID
 * @return array Array of statistics by date
 */
function getEventCheckInStats($eventId) {
    global $pdo;

    // Get event details
    $event = getEventById($eventId);
    if (!$event) {
        return [];
    }

    // Generate dates array
    $dates = generateDateRange($event['start_date'], $event['end_date']);

    // Get total attendees count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM attendees WHERE event_id = ?");
    $stmt->execute([$eventId]);
    $totalAttendees = $stmt->fetchColumn();

    $stats = [];

    foreach ($dates as $date) {
        // Get checked-in count for this date
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT attendee_id)
            FROM checkin_logs
            WHERE event_id = ? AND checkin_date = ?
        ");
        $stmt->execute([$eventId, $date]);
        $checkedInCount = $stmt->fetchColumn();

        // Calculate non-checked-in count
        $nonCheckedInCount = $totalAttendees - $checkedInCount;

        // Calculate percentage
        $percentage = $totalAttendees > 0 ? round(($checkedInCount / $totalAttendees) * 100, 1) : 0;

        $stats[$date] = [
            'date' => $date,
            'formatted_date' => date('d M Y (D)', strtotime($date)),
            'total' => $totalAttendees,
            'checked_in' => $checkedInCount,
            'not_checked_in' => $nonCheckedInCount,
            'percentage' => $percentage
        ];
    }

    return $stats;
}
