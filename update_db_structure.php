<?php
// Include database connection
require_once 'config/database.php';

echo "<h1>Updating Database Structure</h1>";

try {
    // Begin transaction
    $pdo->beginTransaction();
    
    // Check if organization column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE 'organization'");
    $organizationExists = $stmt->rowCount() > 0;
    
    // Check if company column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE 'company'");
    $companyExists = $stmt->rowCount() > 0;
    
    // If organization exists but company doesn't, rename organization to company
    if ($organizationExists && !$companyExists) {
        $sql = "ALTER TABLE attendees CHANGE organization company VARCHAR(255)";
        echo "<p>Executing: $sql</p>";
        $pdo->exec($sql);
        echo "<p>Column 'organization' renamed to 'company'.</p>";
    }
    // If neither exists, add company column
    elseif (!$organizationExists && !$companyExists) {
        $sql = "ALTER TABLE attendees ADD COLUMN company VARCHAR(255) AFTER lastname";
        echo "<p>Executing: $sql</p>";
        $pdo->exec($sql);
        echo "<p>Column 'company' added.</p>";
    }
    
    // Check if mobile column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE 'mobile'");
    $mobileExists = $stmt->rowCount() > 0;
    
    // Check if phone column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE 'phone'");
    $phoneExists = $stmt->rowCount() > 0;
    
    // If mobile exists but phone doesn't, rename mobile to phone
    if ($mobileExists && !$phoneExists) {
        $sql = "ALTER TABLE attendees CHANGE mobile phone VARCHAR(50)";
        echo "<p>Executing: $sql</p>";
        $pdo->exec($sql);
        echo "<p>Column 'mobile' renamed to 'phone'.</p>";
    }
    // If neither exists, add phone column
    elseif (!$mobileExists && !$phoneExists) {
        $sql = "ALTER TABLE attendees ADD COLUMN phone VARCHAR(50) AFTER company";
        echo "<p>Executing: $sql</p>";
        $pdo->exec($sql);
        echo "<p>Column 'phone' added.</p>";
    }
    
    // Make sure all non-primary fields are nullable
    $columnsToMakeNullable = ['lastname', 'company', 'phone', 'group', 'department'];
    
    foreach ($columnsToMakeNullable as $column) {
        $stmt = $pdo->query("SHOW COLUMNS FROM attendees LIKE '$column'");
        if ($stmt->rowCount() > 0) {
            $columnInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($columnInfo['Null'] === 'NO') {
                $sql = "ALTER TABLE attendees MODIFY $column VARCHAR(255) NULL";
                echo "<p>Executing: $sql</p>";
                $pdo->exec($sql);
                echo "<p>Column '$column' modified to be nullable.</p>";
            }
        }
    }
    
    // Commit transaction
    $pdo->commit();
    echo "<p style='color:green;font-weight:bold;'>Database structure updated successfully!</p>";
    
    // Display current table structure
    $stmt = $pdo->query("DESCRIBE attendees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Attendees Table Structure</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    echo "<p style='color:red;font-weight:bold;'>Error updating database structure: " . $e->getMessage() . "</p>";
}
?>
