<?php
// Include database connection
require_once 'config/database.php';

// SQL to update the attendees table
$sql = "
-- Alter attendees table to add new fields and rename existing ones
ALTER TABLE attendees 
ADD COLUMN lastname VA<PERSON><PERSON><PERSON>(255) AFTER name,
ADD COLUMN organization VARCHAR(255) AFTER lastname,
ADD COLUMN `group` VARCHAR(255) AFTER organization,
ADD COLUMN mobile VARCHAR(50) AFTER `group`;

-- Update the department field to be nullable
ALTER TABLE attendees MODIFY department VARCHAR(255) NULL;
";

try {
    // Execute the SQL
    $pdo->exec($sql);
    echo "Database updated successfully!";
    echo "<br>Added new fields to attendees table: lastname, organization, group, mobile";
    echo "<br>Made department field nullable";
    
} catch (PDOException $e) {
    echo "Error updating database: " . $e->getMessage();
}
