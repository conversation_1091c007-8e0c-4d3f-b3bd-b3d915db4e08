<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();

// Get event ID from query string
$eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;
$date = isset($_GET['date']) ? $_GET['date'] : null;
$event = null;
$dates = [];

if ($eventId) {
    // Get event details
    $event = getEventById($eventId);

    if ($event) {
        // Generate dates array
        $dates = generateDateRange($event['start_date'], $event['end_date']);
    }
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Export Data</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Export</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Export Check-in Data</h5>

                <form action="../api/export_api.php" method="POST">
                    <div class="mb-3">
                        <label for="eventSelect" class="form-label">Select Event *</label>
                        <select id="eventSelect" name="event_id" class="form-select" required>
                            <option value="">-- Select an event --</option>
                            <?php foreach ($events as $e): ?>
                                <option value="<?= $e['id'] ?>" <?= $eventId == $e['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($e['name']) ?>
                                    (<?= date('d M Y', strtotime($e['start_date'])) ?> - <?= date('d M Y', strtotime($e['end_date'])) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="dateSelect" class="form-label">Select Date (Optional)</label>
                        <select id="dateSelect" name="date" class="form-select">
                            <option value="">All Dates</option>
                            <?php if ($event && !empty($dates)): ?>
                                <?php foreach ($dates as $d): ?>
                                    <option value="<?= $d ?>" <?= $date == $d ? 'selected' : '' ?>>
                                        <?= date('d M Y (l)', strtotime($d)) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <div class="form-text">Leave empty to export data for all dates.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Export Format *</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="formatCSV" value="csv" checked>
                            <label class="form-check-label" for="formatCSV">
                                CSV (.csv)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="formatExcel" value="excel">
                            <label class="form-check-label" for="formatExcel">
                                Excel (.xlsx)
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Export Type *</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="type" id="typeAttendees" value="attendees" checked>
                            <label class="form-check-label" for="typeAttendees">
                                All Attendees (with check-in status)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="type" id="typeCheckins" value="checkins">
                            <label class="form-check-label" for="typeCheckins">
                                Only Checked-in Attendees
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="type" id="typeSummary" value="summary">
                            <label class="form-check-label" for="typeSummary">
                                Summary Report (counts by date)
                            </label>
                        </div>
                    </div>

                    <div class="mb-3 export-fields" id="exportFieldsContainer">
                        <label class="form-label">Select Fields to Export</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldNumber" value="number" checked>
                                    <label class="form-check-label" for="fieldNumber">Number</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldName" value="name" checked>
                                    <label class="form-check-label" for="fieldName">Name</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldDepartment" value="department" checked>
                                    <label class="form-check-label" for="fieldDepartment">Department/Company</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldEmail" value="email">
                                    <label class="form-check-label" for="fieldEmail">Email</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldPhone" value="phone">
                                    <label class="form-check-label" for="fieldPhone">Phone</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldEventName" value="event_name">
                                    <label class="form-check-label" for="fieldEventName">Event Name</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldEventDate" value="event_date">
                                    <label class="form-check-label" for="fieldEventDate">Event Date</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldCheckinStatus" value="checkin_status" checked>
                                    <label class="form-check-label" for="fieldCheckinStatus">Check-in Status</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldCheckinDate" value="checkin_date">
                                    <label class="form-check-label" for="fieldCheckinDate">Check-in Date</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="fields[]" id="fieldCheckinTime" value="checkin_time" checked>
                                    <label class="form-check-label" for="fieldCheckinTime">Check-in Time</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Export</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Event selection change
document.getElementById('eventSelect').addEventListener('change', function() {
    if (this.value) {
        window.location.href = 'export.php?event_id=' + this.value;
    } else {
        window.location.href = 'export.php';
    }
});

// Date selection change
document.getElementById('dateSelect').addEventListener('change', function() {
    const eventId = document.getElementById('eventSelect').value;
    if (eventId && this.value) {
        window.location.href = 'export.php?event_id=' + eventId + '&date=' + this.value;
    } else if (eventId) {
        window.location.href = 'export.php?event_id=' + eventId;
    }
});

// Toggle field visibility based on export type
function toggleFieldsVisibility() {
    const exportType = document.querySelector('input[name="type"]:checked').value;
    const fieldsContainer = document.getElementById('exportFieldsContainer');

    if (exportType === 'summary') {
        fieldsContainer.style.display = 'none';
    } else {
        fieldsContainer.style.display = 'block';

        // Toggle specific fields based on export type
        const checkinFields = document.querySelectorAll('#fieldCheckinStatus, #fieldCheckinDate, #fieldCheckinTime');
        const eventFields = document.querySelectorAll('#fieldEventName, #fieldEventDate');

        if (exportType === 'attendees') {
            // For attendees, show check-in status fields
            checkinFields.forEach(field => {
                field.closest('.form-check').style.display = 'block';
            });
        } else if (exportType === 'checkins') {
            // For check-ins, hide check-in status (it's always "Checked In")
            document.getElementById('fieldCheckinStatus').closest('.form-check').style.display = 'none';
            document.getElementById('fieldCheckinDate').closest('.form-check').style.display = 'block';
            document.getElementById('fieldCheckinTime').closest('.form-check').style.display = 'block';
        }
    }
}

// Add event listeners to export type radio buttons
document.querySelectorAll('input[name="type"]').forEach(radio => {
    radio.addEventListener('change', toggleFieldsVisibility);
});

// Initialize field visibility
document.addEventListener('DOMContentLoaded', toggleFieldsVisibility);

// Select/Deselect All Fields
function addSelectAllCheckbox() {
    const container = document.getElementById('exportFieldsContainer');
    const firstColumn = container.querySelector('.col-md-6');

    const selectAllDiv = document.createElement('div');
    selectAllDiv.className = 'form-check mb-2';
    selectAllDiv.innerHTML = `
        <input class="form-check-input" type="checkbox" id="selectAllFields" checked>
        <label class="form-check-label fw-bold" for="selectAllFields">Select/Deselect All</label>
    `;

    firstColumn.insertBefore(selectAllDiv, firstColumn.firstChild);

    document.getElementById('selectAllFields').addEventListener('change', function() {
        const isChecked = this.checked;
        document.querySelectorAll('input[name="fields[]"]').forEach(checkbox => {
            if (checkbox.closest('.form-check').style.display !== 'none') {
                checkbox.checked = isChecked;
            }
        });
    });
}

addSelectAllCheckbox();
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>