<?php
// Include header
require_once 'includes/header.php';

// Get statistics
global $pdo;

// Total events
$stmt = $pdo->query("SELECT COUNT(*) FROM events");
$totalEvents = $stmt->fetchColumn();

// Total attendees
$stmt = $pdo->query("SELECT COUNT(*) FROM attendees");
$totalAttendees = $stmt->fetchColumn();

// Total check-ins
$stmt = $pdo->query("SELECT COUNT(*) FROM checkin_logs");
$totalCheckins = $stmt->fetchColumn();

// Recent events
$stmt = $pdo->query("SELECT * FROM events ORDER BY created_at DESC LIMIT 5");
$recentEvents = $stmt->fetchAll();

// Recent check-ins
$stmt = $pdo->query("
    SELECT cl.*, a.name, a.number, a.department, e.name as event_name
    FROM checkin_logs cl
    JOIN attendees a ON cl.attendee_id = a.id
    JOIN events e ON cl.event_id = e.id
    ORDER BY cl.created_at DESC
    LIMIT 10
");
$recentCheckins = $stmt->fetchAll();
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Admin Dashboard</h1>
        <p class="lead">Welcome, <?= htmlspecialchars($_SESSION['user_name']) ?>!</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body dashboard-stats">
                <div class="number"><?= $totalEvents ?></div>
                <div class="label">Total Events</div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body dashboard-stats">
                <div class="number"><?= $totalAttendees ?></div>
                <div class="label">Total Attendees</div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body dashboard-stats">
                <div class="number"><?= $totalCheckins ?></div>
                <div class="label">Total Check-ins</div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Events</h5>
                <a href="../public/event_create.php" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle"></i> Create New Event
                </a>
            </div>
            <div class="card-body">
                <?php if (count($recentEvents) > 0): ?>
                <div class="list-group">
                    <?php foreach ($recentEvents as $event): ?>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><?= htmlspecialchars($event['name']) ?></h5>
                            <small><?= date('d M Y', strtotime($event['created_at'])) ?></small>
                        </div>
                        <p class="mb-1 event-dates">
                            <i class="bi bi-calendar"></i>
                            <?= date('d M Y', strtotime($event['start_date'])) ?> -
                            <?= date('d M Y', strtotime($event['end_date'])) ?>
                        </p>
                        <div class="mt-2 d-flex flex-wrap">
                            <a href="../public/checkin.php?event_id=<?= $event['id'] ?>" class="btn btn-sm btn-primary me-2 mb-2">
                                <i class="bi bi-box-arrow-in-right"></i> Check-in
                            </a>
                            <a href="../public/upload_csv.php?event_id=<?= $event['id'] ?>" class="btn btn-sm btn-info me-2 mb-2">
                                <i class="bi bi-upload"></i> Upload CSV
                            </a>
                            <button type="button" class="btn btn-sm btn-danger me-2 mb-2 delete-event-btn" data-event-id="<?= $event['id'] ?>" data-event-name="<?= htmlspecialchars($event['name']) ?>">
                                <i class="bi bi-trash"></i> Delete Event
                            </button>
                            <?php
                            // Calculate the number of days in the event
                            $startDate = new DateTime($event['start_date']);
                            $endDate = new DateTime($event['end_date']);
                            $interval = $startDate->diff($endDate);
                            $numDays = $interval->days + 1; // Include both start and end days

                            // Create an export button for each day
                            for ($day = 1; $day <= $numDays; $day++) {
                                $currentDate = clone $startDate;
                                $currentDate->modify('+' . ($day - 1) . ' days');
                                $formattedDate = $currentDate->format('Y-m-d');
                            ?>
                            <form action="../api/export_api.php" method="POST" class="d-inline quick-export-form mb-2 me-2">
                                <input type="hidden" name="event_id" value="<?= $event['id'] ?>">
                                <input type="hidden" name="format" value="csv">
                                <input type="hidden" name="type" value="attendees">
                                <input type="hidden" name="specific_date" value="<?= $formattedDate ?>">
                                <input type="hidden" name="fields[]" value="number">
                                <input type="hidden" name="fields[]" value="name">
                                <input type="hidden" name="fields[]" value="department">
                                <input type="hidden" name="fields[]" value="event_date">
                                <input type="hidden" name="fields[]" value="checkin_status">
                                <input type="hidden" name="fields[]" value="checkin_time">
                                <button type="submit" class="btn btn-sm btn-success">
                                    <i class="bi bi-file-excel"></i> Export Day <?= $day ?>
                                </button>
                            </form>
                            <?php } ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    No events found. <a href="../public/event_create.php" class="alert-link">Create your first event</a>.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Check-ins</h5>
                <a href="checkin_logs.php" class="btn btn-sm btn-secondary">View All</a>
            </div>
            <div class="card-body">
                <?php if (count($recentCheckins) > 0): ?>
                <div class="list-group">
                    <?php foreach ($recentCheckins as $checkin): ?>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">
                                <span class="attendee-number">#<?= $checkin['number'] ?></span>
                                <?= htmlspecialchars($checkin['name']) ?>
                            </h5>
                            <small><?= date('H:i', strtotime($checkin['checkin_time'])) ?></small>
                        </div>
                        <p class="mb-1">
                            <span class="badge bg-primary"><?= htmlspecialchars($checkin['event_name']) ?></span>
                            <span class="badge bg-secondary"><?= date('d M Y', strtotime($checkin['checkin_date'])) ?></span>
                            <span class="badge bg-info"><?= htmlspecialchars($checkin['department']) ?></span>
                        </p>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    No check-ins recorded yet.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- <div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="../public/event_create.php" class="btn btn-primary d-block">
                            <i class="bi bi-plus-circle"></i> Create Event
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="../public/checkin.php" class="btn btn-success d-block">
                            <i class="bi bi-box-arrow-in-right"></i> Check-in
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="../public/upload_csv.php" class="btn btn-info d-block">
                            <i class="bi bi-upload"></i> Upload CSV
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="export.php" class="btn btn-secondary d-block">
                            <i class="bi bi-download"></i> Export Data
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle quick export forms
    const quickExportForms = document.querySelectorAll('.quick-export-form');

    quickExportForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Create a hidden iframe for download if it doesn't exist
            let iframe = document.getElementById('downloadFrame');
            if (!iframe) {
                iframe = document.createElement('iframe');
                iframe.id = 'downloadFrame';
                iframe.name = 'downloadFrame';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
            }

            // Set the form target to the iframe
            form.target = 'downloadFrame';

            // Show a temporary success message
            const button = form.querySelector('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i> Exporting...';
            button.disabled = true;

            // Submit the form
            form.submit();

            // Reset the button after a delay
            setTimeout(function() {
                button.innerHTML = '<i class="bi bi-check"></i> Downloaded!';

                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 2000);
            }, 1000);
        });
    });

    // Handle delete event buttons
    const deleteButtons = document.querySelectorAll('.delete-event-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteEventModal'));
    let eventIdToDelete = null;

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const eventId = this.getAttribute('data-event-id');
            const eventName = this.getAttribute('data-event-name');

            // Set the event name in the modal
            document.getElementById('eventNameToDelete').textContent = eventName;

            // Store the event ID for later use
            eventIdToDelete = eventId;

            // Show the modal
            deleteModal.show();
        });
    });

    // Handle confirm delete button
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (eventIdToDelete) {
            // Show loading state
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            this.disabled = true;

            // Send delete request
            fetch('../api/delete_event_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `event_id=${eventIdToDelete}`
            })
            .then(response => response.json())
            .then(data => {
                // Hide the modal
                deleteModal.hide();

                if (data.success) {
                    // Show success message with toast
                    const toastEl = document.createElement('div');
                    toastEl.className = 'position-fixed bottom-0 end-0 p-3';
                    toastEl.style.zIndex = '11';
                    toastEl.innerHTML = `
                        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="d-flex">
                                <div class="toast-body">
                                    <i class="bi bi-check-circle-fill me-2"></i> ${data.message}
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(toastEl);

                    const toast = new bootstrap.Toast(toastEl.querySelector('.toast'));
                    toast.show();

                    // Reload the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    alert('Error: ' + data.message);

                    // Reset button
                    this.innerHTML = 'Delete Event';
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the event. Please try again.');

                // Hide the modal
                deleteModal.hide();

                // Reset button
                this.innerHTML = 'Delete Event';
                this.disabled = false;
            });
        }
    });
});
</script>

<?php
// Add delete confirmation modal
?>

<!-- Delete Event Confirmation Modal -->
<div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the event "<span id="eventNameToDelete"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This will permanently delete all attendees and check-in data associated with this event. This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Event</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>