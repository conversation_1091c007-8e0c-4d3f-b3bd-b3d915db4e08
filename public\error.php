<?php
// Include header
require_once 'includes/header.php';

// Get message from session or use default
$message = $_SESSION['error_message'] ?? 'An error occurred.';
$returnUrl = $_SESSION['return_url'] ?? 'index.php';
$returnText = $_SESSION['return_text'] ?? 'Return to Home';

// Clear session variables
unset($_SESSION['error_message']);
unset($_SESSION['return_url']);
unset($_SESSION['return_text']);
?>

<div class="row">
    <div class="col-md-8 offset-md-2 mt-5">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-4">
                    <i class="bi bi-exclamation-circle text-danger" style="font-size: 5rem;"></i>
                </div>
                <h2 class="card-title">Error</h2>
                <p class="card-text"><?= htmlspecialchars($message) ?></p>
                <a href="<?= htmlspecialchars($returnUrl) ?>" class="btn btn-primary mt-3">
                    <?= htmlspecialchars($returnText) ?>
                </a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>