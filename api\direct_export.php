<?php
// Include initialization file
require_once '../config/init.php';

// Check if event_id is provided
if (!isset($_GET['event_id']) || empty($_GET['event_id'])) {
    $_SESSION['error_message'] = "Event ID is required.";
    redirect('../public/error.php');
}

// Get parameters
$eventId = (int)$_GET['event_id'];
$date = isset($_GET['date']) && !empty($_GET['date']) ? $_GET['date'] : null;
$format = isset($_GET['format']) && in_array($_GET['format'], ['csv', 'excel']) ? $_GET['format'] : 'excel';
$type = isset($_GET['type']) && in_array($_GET['type'], ['attendees', 'checkins', 'summary']) ? $_GET['type'] : 'attendees';

// Get event details
$event = getEventById($eventId);

// Check if event exists
if (!$event) {
    $_SESSION['error_message'] = "Event not found.";
    redirect('../public/error.php');
}

// Validate date if provided
if ($date) {
    $dates = generateDateRange($event['start_date'], $event['end_date']);
    if (!in_array($date, $dates)) {
        $_SESSION['error_message'] = "Invalid date for this event.";
        redirect('../public/error.php');
    }
}

// Generate filename
if ($date) {
    // Calculate day number if date is provided
    $startDate = new DateTime($event['start_date']);
    $currentDate = new DateTime($date);
    $dayDiff = $startDate->diff($currentDate)->days;
    $dayNumber = $dayDiff + 1; // Day 1, Day 2, etc.

    $filename = sanitize($event['name']) . '_Day' . $dayNumber . '_' . date('Y-m-d', strtotime($date)) . '_' . $type;
} else {
    $filename = sanitize($event['name']) . '_' . $type;
}

// Define default fields to export
$fields = ['number', 'name', 'company', 'phone', 'group', 'event_date', 'checkin_status', 'checkin_time'];

try {
    global $pdo;

    // Get data for attendees
    $data = [];
    $headers = [];

    // Define field mappings (database field => display name)
    $fieldMappings = [
        'number' => 'No.',
        'name' => 'Name',
        'company' => 'Company',
        'group' => 'Group',
        'phone' => 'Phone',
        'event_name' => 'Event Name',
        'event_date' => 'Event Date',
        'checkin_status' => 'Check-in Status',
        'checkin_date' => 'Check-in Date',
        'checkin_time' => 'Check-in Time'
    ];

    // Build headers based on selected fields
    foreach ($fields as $field) {
        if (isset($fieldMappings[$field])) {
            $headers[] = $fieldMappings[$field];
        }
    }

    // Build SQL query
    $sql = "SELECT a.*, e.name as event_name";

    if ($date) {
        $sql .= ", CASE WHEN cl.id IS NOT NULL THEN 'Checked In' ELSE 'Not Checked In' END AS checkin_status,
                  cl.checkin_date, cl.checkin_time";
        $sql .= " FROM attendees a
                  JOIN events e ON a.event_id = e.id
                  LEFT JOIN checkin_logs cl ON a.id = cl.attendee_id AND cl.event_id = ? AND cl.checkin_date = ?
                  WHERE a.event_id = ?
                  ORDER BY a.number ASC";
        $params = [$eventId, $date, $eventId];
    } else {
        $sql .= " FROM attendees a
                  JOIN events e ON a.event_id = e.id
                  WHERE a.event_id = ?
                  ORDER BY a.number ASC";
        $params = [$eventId];
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $attendees = $stmt->fetchAll();

    // Process each attendee
    foreach ($attendees as $attendee) {
        $row = [];

        // Add selected fields to the row
        foreach ($fields as $field) {
            switch ($field) {
                case 'number':
                    $row[] = $attendee['number'];
                    break;
                case 'name':
                    $row[] = $attendee['name'];
                    break;
                case 'company':
                    $row[] = $attendee['company'] ?? '';
                    break;
                case 'group':
                    $row[] = $attendee['group'] ?? '';
                    break;
                case 'phone':
                    $row[] = $attendee['phone'] ?? '';
                    break;
                case 'event_name':
                    $row[] = $attendee['event_name'];
                    break;
                case 'event_date':
                    $row[] = $date ?? date('Y-m-d', strtotime($event['start_date'])) . ' to ' . date('Y-m-d', strtotime($event['end_date']));
                    break;
                case 'checkin_status':
                    $row[] = isset($attendee['checkin_status']) ? $attendee['checkin_status'] : 'Not Checked In';
                    break;
                case 'checkin_date':
                    $row[] = isset($attendee['checkin_date']) ? $attendee['checkin_date'] : '';
                    break;
                case 'checkin_time':
                    $row[] = isset($attendee['checkin_time']) ? $attendee['checkin_time'] : '';
                    break;
            }
        }

        $data[] = $row;
    }

    // Export data based on format
    if ($format === 'csv') {
        exportToCSV($data, $headers, $filename);
    } else {
        exportToExcel($data, $headers, $filename);
    }

} catch (PDOException $e) {
    // Set error message and redirect
    $_SESSION['error_message'] = "Error exporting data: " . $e->getMessage();
    redirect('../public/error.php');
}
