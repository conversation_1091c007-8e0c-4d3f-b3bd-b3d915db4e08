<?php
// Include header
require_once 'includes/header.php';

// Get all events
$events = getAllEvents();

// Check if event_id is provided
$eventId = isset($_GET['event_id']) ? (int)$_GET['event_id'] : null;
$event = null;
$attendees = [];
$dates = [];
$selectedDate = null;

if ($eventId) {
    // Get event details
    $event = getEventById($eventId);

    if ($event) {
        // Generate dates array
        $dates = generateDateRange($event['start_date'], $event['end_date']);

        // Get selected date or use current date if within range
        $today = date('Y-m-d');
        $selectedDate = isset($_GET['date']) ? $_GET['date'] : null;

        // If no date selected or selected date not in range, use today if in range, otherwise use first date
        if (!$selectedDate || !in_array($selectedDate, $dates)) {
            if (in_array($today, $dates)) {
                $selectedDate = $today;
            } else {
                $selectedDate = $dates[0] ?? null;
            }
        }

        // Get attendees for this event
        $attendees = getAttendeesByEventId($eventId);
    }
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Check-in</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Check-in</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Event</h5>
                <select id="eventSelect" class="form-select">
                    <option value="">-- Select an event --</option>
                    <?php foreach ($events as $e): ?>
                        <option value="<?= $e['id'] ?>" <?= $eventId == $e['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($e['name']) ?>
                            (<?= date('d M Y', strtotime($e['start_date'])) ?> - <?= date('d M Y', strtotime($e['end_date'])) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>

    <?php if ($event && !empty($dates)): ?>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Select Date</h5>
                <select id="dateSelect" class="form-select">
                    <?php foreach ($dates as $date): ?>
                        <option value="<?= $date ?>" <?= $selectedDate == $date ? 'selected' : '' ?>>
                            <?= date('d M Y (l)', strtotime($date)) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if ($event && $selectedDate): ?>
<?php
    // Get check-in statistics for this event and date
    $stats = getEventCheckInStats($eventId);
    $dateStats = $stats[$selectedDate] ?? null;

    $checkedInCount = $dateStats ? $dateStats['checked_in'] : 0;
    $notCheckedInCount = $dateStats ? $dateStats['not_checked_in'] : 0;
    $totalCount = $dateStats ? $dateStats['total'] : 0;
    $percentage = $dateStats ? $dateStats['percentage'] : 0;
?>
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">
                        <?= htmlspecialchars($event['name']) ?> -
                        <?= date('d M Y (l)', strtotime($selectedDate)) ?>
                    </h5>
                    <div>
                        <form id="quickExportForm" action="../api/export_api.php" method="POST" class="d-inline" target="downloadFrame">
                            <input type="hidden" name="event_id" value="<?= $eventId ?>">
                            <input type="hidden" name="date" value="<?= $selectedDate ?>">
                            <input type="hidden" name="format" value="excel">
                            <input type="hidden" name="type" value="attendees">
                            <input type="hidden" name="fields[]" value="number">
                            <input type="hidden" name="fields[]" value="name">
                            <input type="hidden" name="fields[]" value="company">
                            <input type="hidden" name="fields[]" value="phone">
                            <input type="hidden" name="fields[]" value="group">
                            <input type="hidden" name="fields[]" value="event_date">
                            <input type="hidden" name="fields[]" value="checkin_status">
                            <input type="hidden" name="fields[]" value="checkin_time">
                            <a href="#" id="quickExportBtn" class="btn btn-success">
                                <i class="bi bi-download me-1"></i> Export to Excel
                            </a>
                        </form>
                        <!-- <a href="export.php?event_id=<?= $eventId ?>&date=<?= $selectedDate ?>" class="btn btn-outline-primary ms-2">
                            <i class="bi bi-gear me-1"></i> Advanced Export
                        </a> -->

                        <!-- Status message for quick export -->
                        <div id="exportStatus" class="mt-2" style="display: none;">
                            <div class="alert alert-info p-2 small">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status" style="width: 1rem; height: 1rem;">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span id="statusMessage">Exporting data...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Check-in Statistics -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body p-3 text-center">
                                <h3 class="mb-0"><?= $totalCount ?></h3>
                                <small>Total Attendees</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body p-3 text-center">
                                <h3 class="mb-0"><?= $checkedInCount ?> (<?= $percentage ?>%)</h3>
                                <small>Checked In</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-secondary text-white">
                            <div class="card-body p-3 text-center">
                                <h3 class="mb-0"><?= $notCheckedInCount ?></h3>
                                <small>Not Checked In</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Direct Export Link -->
                <div class="mb-3 text-end">
                    <a href="../api/direct_export.php?event_id=<?= $eventId ?>&date=<?= $selectedDate ?>&format=excel&type=attendees" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-file-excel"></i> Alternative Export
                    </a>
                </div>

                <div class="search-box">
                    <i class="bi bi-search"></i>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search by name, number, or department...">
                </div>

                <?php if (empty($attendees)): ?>
                <div class="alert alert-warning">
                    No attendees found for this event. <a href="upload_csv.php?event_id=<?= $eventId ?>" class="alert-link">Upload a CSV file</a> to add attendees.
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($attendees as $attendee): ?>
                        <?php
                        $isCheckedIn = isCheckedIn($attendee['id'], $eventId, $selectedDate);
                        $cardClass = $isCheckedIn ? 'checked-in' : 'not-checked-in';
                        $buttonClass = $isCheckedIn ? 'btn-success' : 'btn-primary';
                        $buttonText = $isCheckedIn ? '✓ Done' : 'Check-in';

                        // Get check-in time if checked in
                        $checkinTime = '';
                        if ($isCheckedIn) {
                            global $pdo;
                            $stmt = $pdo->prepare("SELECT checkin_time FROM checkin_logs WHERE attendee_id = ? AND event_id = ? AND checkin_date = ?");
                            $stmt->execute([$attendee['id'], $eventId, $selectedDate]);
                            $checkinTime = $stmt->fetchColumn();
                        }
                        ?>
                        <div class="col-md-6 mb-3 attendee-card">
                            <div class="card checkin-card <?= $cardClass ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="card-title">
                                                <span class="attendee-number">#<?= $attendee['number'] ?></span>
                                                <?= htmlspecialchars($attendee['name']) ?>
                                            </h5>
                                            <p class="card-text">
                                                <?= htmlspecialchars($attendee['company'] ?? '') ?>
                                                <?php if (!empty($attendee['group'])): ?>
                                                <span class="badge bg-secondary"><?= htmlspecialchars($attendee['group']) ?></span>
                                                <?php endif; ?>
                                            </p>
                                            <?php if (!empty($attendee['phone'])): ?>
                                            <p class="card-text small text-muted">
                                                <i class="bi bi-phone"></i> <?= htmlspecialchars($attendee['phone']) ?>
                                            </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <p class="checkin-status <?= $cardClass ?>">
                                                <?= $isCheckedIn ? 'Checked In' : 'Not Checked In' ?>
                                            </p>
                                            <button
                                                class="btn <?= $buttonClass ?> btn-checkin"
                                                data-attendee-id="<?= $attendee['id'] ?>"
                                                data-event-id="<?= $eventId ?>"
                                                data-date="<?= $selectedDate ?>"
                                                data-action="<?= $isCheckedIn ? 'checkout' : 'checkin' ?>"
                                            >
                                                <?= $buttonText ?>
                                            </button>
                                            <?php if ($isCheckedIn && $checkinTime): ?>
                                            <p class="checkin-time mt-2 text-muted">
                                                <small>Checked in at: <?= date('H:i:s', strtotime($checkinTime)) ?></small>
                                            </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php elseif (!$event && count($events) > 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            Please select an event to proceed with check-in.
        </div>
    </div>
</div>
<?php elseif (count($events) == 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-warning">
            No events found. <a href="event_create.php" class="alert-link">Create your first event</a>.
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Handle check-in/check-out functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all check-in buttons
    const checkinButtons = document.querySelectorAll('.btn-checkin');
    checkinButtons.forEach(button => {
        button.addEventListener('click', function() {
            const attendeeId = this.dataset.attendeeId;
            const eventId = this.dataset.eventId;
            const date = this.dataset.date;
            const action = this.dataset.action;
            const card = this.closest('.attendee-card');

            console.log('Button clicked:', {
                attendeeId,
                eventId,
                date,
                action
            });

            // Send AJAX request
            $.ajax({
                url: "../api/checkin_api.php",
                type: "POST",
                data: {
                    attendee_id: attendeeId,
                    event_id: eventId,
                    date: date,
                    action: action
                },
                success: function(response) {
                    console.log('API Response:', response);
                    const data = JSON.parse(response);
                    console.log('Parsed data:', data);
                    if (data.success) {
                        if (action === 'checkin') {
                            // Update UI for check-in
                            card.find(".checkin-status")
                                .removeClass("not-checked-in")
                                .addClass("checked-in")
                                .text("Checked In");

                            // Update button to "Done" state
                            const button = card.find(".btn-checkin");
                            button.removeClass("btn-primary")
                                .addClass("btn-success")
                                .text("✓ Done")
                                .attr("data-action", "checkout");

                            // Update card class
                            card.find(".checkin-card")
                                .removeClass("not-checked-in")
                                .addClass("checked-in");

                            // Add check-in time
                            if (data.checkin_time) {
                                const timeStr = new Date('1970-01-01T' + data.checkin_time).toLocaleTimeString();
                                const timeElement = document.createElement('p');
                                timeElement.className = 'checkin-time mt-2 text-muted';
                                timeElement.innerHTML = '<small>Checked in at: ' + timeStr + '</small>';

                                // Check if time element already exists
                                const existingTime = card.find('.checkin-time');
                                if (existingTime.length > 0) {
                                    existingTime.replaceWith(timeElement);
                                } else {
                                    card.find('.text-end').append(timeElement);
                                }
                            }
                        } else if (action === 'checkout') {
                            console.log('Processing checkout action');
                            // Update UI for check-out
                            card.find(".checkin-status")
                                .removeClass("checked-in")
                                .addClass("not-checked-in")
                                .text("Not Checked In");

                            // Update button to "Check-in" state
                            const button = card.find(".btn-checkin");
                            button.removeClass("btn-success")
                                .addClass("btn-primary")
                                .text("Check-in")
                                .attr("data-action", "checkin");

                            // Update card class
                            card.find(".checkin-card")
                                .removeClass("checked-in")
                                .addClass("not-checked-in");

                            // Remove check-in time
                            card.find('.checkin-time').remove();
                        }
                    } else {
                        // Show error message
                        alert("Error: " + data.message);
                    }
                },
                error: function() {
                    alert("An error occurred. Please try again.");
                }
            });
        });
    });

    // Handle quick export button
    const quickExportBtn = document.getElementById('quickExportBtn');
    if (quickExportBtn) {
        quickExportBtn.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent the default anchor behavior

            console.log('Export button clicked');

            // Show status message
            const statusElement = document.getElementById('exportStatus');
            const statusMessage = document.getElementById('statusMessage');
            statusElement.style.display = 'block';
            statusMessage.textContent = 'Exporting data...';

            // Create a hidden iframe for download if it doesn't exist
            let iframe = document.getElementById('downloadFrame');
            if (!iframe) {
                iframe = document.createElement('iframe');
                iframe.id = 'downloadFrame';
                iframe.name = 'downloadFrame';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
            }

            // Get the form and submit it
            const form = document.getElementById('quickExportForm');
            console.log('Submitting form:', form);
            form.submit();

            // Update status message
            setTimeout(function() {
                statusMessage.textContent = 'Export complete!';
                statusElement.querySelector('.alert').classList.remove('alert-info');
                statusElement.querySelector('.alert').classList.add('alert-success');
                statusElement.querySelector('.spinner-border').style.display = 'none';

                // Hide status after 3 seconds
                setTimeout(function() {
                    statusElement.style.display = 'none';
                    // Reset alert classes
                    statusElement.querySelector('.alert').classList.add('alert-info');
                    statusElement.querySelector('.alert').classList.remove('alert-success');
                    statusElement.querySelector('.spinner-border').style.display = 'inline-block';
                }, 3000);
            }, 1000);
        });
    }
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>