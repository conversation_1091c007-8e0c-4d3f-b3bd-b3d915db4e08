<?php
// Include database connection and functions
require_once 'config/database.php';
require_once 'config/functions.php';

// Set up test environment
$eventId = 1; // Use an existing event ID or create a new one
$csvFile = 'sample_attendees.csv';

echo "<h1>CSV Upload Test</h1>";

// Check if the event exists
$event = getEventById($eventId);
if (!$event) {
    echo "<p class='error'>Event with ID $eventId not found. Please create an event first.</p>";
    exit;
}

echo "<p>Using event: " . htmlspecialchars($event['name']) . " (ID: $eventId)</p>";

// Check if the CSV file exists
if (!file_exists($csvFile)) {
    echo "<p class='error'>CSV file not found: $csvFile</p>";
    exit;
}

echo "<p>Using CSV file: $csvFile</p>";
echo "<h2>CSV File Contents:</h2>";
echo "<pre>";
echo file_get_contents($csvFile);
echo "</pre>";

// Process the CSV file
echo "<h2>Processing CSV File</h2>";

try {
    // Open the file
    $file = fopen($csvFile, 'r');
    if (!$file) {
        throw new Exception("Error opening file.");
    }
    
    // Define column indexes
    $columnIndexes = [
        'No' => 0,           // First column is the number
        'Name' => 1,         // Second column is the name
        'Lastname' => 2,     // Third column is the lastname
        'Mobile' => 3,       // Fourth column is the mobile
        'Organization' => 4, // Fifth column is the organization
        'Group' => 5,        // Sixth column is the group
        'Department' => null // No department column
    ];
    
    // Begin transaction
    $pdo->beginTransaction();
    
    // Prepare statement for inserting attendees
    $stmt = $pdo->prepare("INSERT INTO attendees (event_id, number, name, lastname, organization, `group`, mobile, department) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    
    // Counter for successful inserts
    $insertCount = 0;
    
    // Process each row
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Row</th><th>Number</th><th>Name</th><th>Lastname</th><th>Mobile</th><th>Organization</th><th>Group</th><th>Status</th></tr>";
    
    $rowNumber = 0;
    while (($row = fgetcsv($file)) !== false) {
        $rowNumber++;
        
        // Skip empty rows
        if (empty($row) || count($row) < 6) {
            echo "<tr><td>$rowNumber</td><td colspan='6'>Skipped - Not enough columns</td><td>Skipped</td></tr>";
            continue;
        }
        
        // Get data from row
        $number = (int)$row[$columnIndexes['No']]; // Convert to integer
        $name = $row[$columnIndexes['Name']];
        $lastname = $row[$columnIndexes['Lastname']];
        $mobile = $row[$columnIndexes['Mobile']];
        $organization = $row[$columnIndexes['Organization']];
        $group = $row[$columnIndexes['Group']];
        $department = $columnIndexes['Department'] !== null && isset($row[$columnIndexes['Department']]) ? $row[$columnIndexes['Department']] : null;
        
        echo "<tr>";
        echo "<td>$rowNumber</td>";
        echo "<td>" . htmlspecialchars($number) . "</td>";
        echo "<td>" . htmlspecialchars($name) . "</td>";
        echo "<td>" . htmlspecialchars($lastname) . "</td>";
        echo "<td>" . htmlspecialchars($mobile) . "</td>";
        echo "<td>" . htmlspecialchars($organization) . "</td>";
        echo "<td>" . htmlspecialchars($group) . "</td>";
        
        // Skip rows with empty required fields
        if (empty($name) || empty($lastname)) {
            echo "<td>Skipped - Missing name or lastname</td>";
            echo "</tr>";
            continue;
        }
        
        try {
            // Insert into database
            $params = [
                $eventId,      // event_id
                $number,       // number
                $name,         // name
                $lastname,     // lastname
                $organization, // organization
                $group,        // group
                $mobile,       // mobile
                $department    // department
            ];
            
            $stmt->execute($params);
            $insertCount++;
            echo "<td>Inserted</td>";
        } catch (PDOException $e) {
            echo "<td>Error: " . $e->getMessage() . "</td>";
        }
        
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Commit transaction
    $pdo->commit();
    
    // Close the file
    fclose($file);
    
    echo "<h2>Results</h2>";
    echo "<p>Total rows processed: $rowNumber</p>";
    echo "<p>Successful inserts: $insertCount</p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // Close the file if it's open
    if (isset($file) && $file) {
        fclose($file);
    }
    
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}
?>
