<?php
// Include initialization file
require_once '../config/init.php';

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error_message'] = "Invalid request method.";
    redirect('../public/error.php');
}

// Check if event_id is provided
if (!isset($_POST['event_id']) || empty($_POST['event_id'])) {
    $_SESSION['error_message'] = "Event ID is required.";
    $_SESSION['return_url'] = '../public/export.php';
    redirect('../public/error.php');
}

// Get parameters
$eventId = (int)$_POST['event_id'];
$date = isset($_POST['date']) && !empty($_POST['date']) ? $_POST['date'] : null;
// Also check for specific_date parameter (used by day-specific export buttons)
if (!$date && isset($_POST['specific_date']) && !empty($_POST['specific_date'])) {
    $date = $_POST['specific_date'];
}
$format = isset($_POST['format']) && in_array($_POST['format'], ['csv', 'excel']) ? $_POST['format'] : 'csv';
$type = isset($_POST['type']) && in_array($_POST['type'], ['attendees', 'checkins', 'summary']) ? $_POST['type'] : 'attendees';

// Get selected fields
$selectedFields = isset($_POST['fields']) && is_array($_POST['fields']) ? $_POST['fields'] : ['number', 'name', 'department'];

// Get event details
$event = getEventById($eventId);

// Check if event exists
if (!$event) {
    $_SESSION['error_message'] = "Event not found.";
    $_SESSION['return_url'] = '../public/export.php';
    redirect('../public/error.php');
}

// Validate date if provided
if ($date) {
    $dates = generateDateRange($event['start_date'], $event['end_date']);
    if (!in_array($date, $dates)) {
        $_SESSION['error_message'] = "Invalid date for this event.";
        $_SESSION['return_url'] = "../public/export.php?event_id=$eventId";
        redirect('../public/error.php');
    }
}

// Generate filename
if ($date) {
    // Calculate day number if date is provided
    $startDate = new DateTime($event['start_date']);
    $currentDate = new DateTime($date);
    $dayDiff = $startDate->diff($currentDate)->days;
    $dayNumber = $dayDiff + 1; // Day 1, Day 2, etc.

    $filename = sanitize($event['name']) . '_Day' . $dayNumber . '_' . date('Y-m-d', strtotime($date)) . '_' . $type;
} else {
    $filename = sanitize($event['name']) . '_' . $type;
}

// Get data based on export type
$data = [];
$headers = [];

try {
    global $pdo;

    if ($type === 'attendees') {
        // Define field mappings (database field => display name)
        $fieldMappings = [
            'number' => 'No.',
            'name' => 'Name',
            'lastname' => 'Last Name',
            'position' => 'Position',
            'company' => 'Company',
            'group' => 'Group',
            'department' => 'Department',
            'email' => 'Email',
            'phone' => 'Phone',
            'event_name' => 'Event Name',
            'event_date' => 'Event Date',
            'checkin_status' => 'Check-in Status',
            'checkin_date' => 'Check-in Date',
            'checkin_time' => 'Check-in Time'
        ];

        // Build headers based on selected fields
        $headers = [];
        foreach ($selectedFields as $field) {
            if (isset($fieldMappings[$field])) {
                $headers[] = $fieldMappings[$field];
            }
        }

        // Build SQL query
        $sql = "SELECT a.*, e.name as event_name";

        if ($date) {
            $sql .= ", CASE WHEN cl.id IS NOT NULL THEN 'Checked In' ELSE 'Not Checked In' END AS checkin_status,
                      cl.checkin_date, cl.checkin_time";
            $sql .= " FROM attendees a
                      JOIN events e ON a.event_id = e.id
                      LEFT JOIN checkin_logs cl ON a.id = cl.attendee_id AND cl.event_id = ? AND cl.checkin_date = ?
                      WHERE a.event_id = ?
                      ORDER BY a.number ASC";
            $params = [$eventId, $date, $eventId];
        } else {
            $sql .= " FROM attendees a
                      JOIN events e ON a.event_id = e.id
                      WHERE a.event_id = ?
                      ORDER BY a.number ASC";
            $params = [$eventId];
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $attendees = $stmt->fetchAll();

        // Process each attendee
        foreach ($attendees as $attendee) {
            $row = [];

            // Add selected fields to the row
            foreach ($selectedFields as $field) {
                switch ($field) {
                    case 'number':
                        $row[] = $attendee['number'];
                        break;
                    case 'name':
                        $row[] = $attendee['name'];
                        break;
                    case 'lastname':
                        $row[] = $attendee['lastname'] ?? '';
                        break;
                    case 'position':
                        $row[] = $attendee['position'] ?? '';
                        break;
                    case 'company':
                        $row[] = $attendee['company'] ?? '';
                        break;
                    case 'group':
                        $row[] = $attendee['group'] ?? '';
                        break;
                    case 'department':
                        $row[] = $attendee['department'];
                        break;
                    case 'email':
                        $row[] = $attendee['email'] ?? '';
                        break;
                    case 'phone':
                        $row[] = $attendee['phone'] ?? '';
                        break;
                    case 'event_name':
                        $row[] = $attendee['event_name'];
                        break;
                    case 'event_date':
                        $row[] = $date ?? date('Y-m-d', strtotime($event['start_date'])) . ' to ' . date('Y-m-d', strtotime($event['end_date']));
                        break;
                    case 'checkin_status':
                        $row[] = isset($attendee['checkin_status']) ? $attendee['checkin_status'] : 'Not Checked In';
                        break;
                    case 'checkin_date':
                        $row[] = isset($attendee['checkin_date']) ? $attendee['checkin_date'] : '';
                        break;
                    case 'checkin_time':
                        $row[] = isset($attendee['checkin_time']) ? $attendee['checkin_time'] : '';
                        break;
                }
            }

            $data[] = $row;
        }
    } elseif ($type === 'checkins') {
        // Define field mappings (database field => display name)
        $fieldMappings = [
            'number' => 'No.',
            'name' => 'Name',
            'lastname' => 'Last Name',
            'position' => 'Position',
            'company' => 'Company',
            'group' => 'Group',
            'department' => 'Department',
            'email' => 'Email',
            'phone' => 'Phone',
            'event_name' => 'Event Name',
            'event_date' => 'Event Date',
            'checkin_date' => 'Check-in Date',
            'checkin_time' => 'Check-in Time'
        ];

        // Build headers based on selected fields
        $headers = [];
        foreach ($selectedFields as $field) {
            if (isset($fieldMappings[$field]) && $field !== 'checkin_status') { // Skip check-in status as it's always "Checked In"
                $headers[] = $fieldMappings[$field];
            }
        }

        // Build SQL query
        $sql = "SELECT a.*, e.name as event_name, cl.checkin_date, cl.checkin_time
                FROM checkin_logs cl
                JOIN attendees a ON cl.attendee_id = a.id
                JOIN events e ON cl.event_id = e.id
                WHERE cl.event_id = ?";

        $params = [$eventId];

        if ($date) {
            $sql .= " AND cl.checkin_date = ?";
            $params[] = $date;
        }

        $sql .= " ORDER BY cl.checkin_date ASC, cl.checkin_time ASC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $checkins = $stmt->fetchAll();

        // Process each check-in
        foreach ($checkins as $checkin) {
            $row = [];

            // Add selected fields to the row
            foreach ($selectedFields as $field) {
                if ($field === 'checkin_status') continue; // Skip check-in status

                switch ($field) {
                    case 'number':
                        $row[] = $checkin['number'];
                        break;
                    case 'name':
                        $row[] = $checkin['name'];
                        break;
                    case 'lastname':
                        $row[] = $checkin['lastname'] ?? '';
                        break;
                    case 'position':
                        $row[] = $checkin['position'] ?? '';
                        break;
                    case 'company':
                        $row[] = $checkin['company'] ?? '';
                        break;
                    case 'group':
                        $row[] = $checkin['group'] ?? '';
                        break;
                    case 'department':
                        $row[] = $checkin['department'];
                        break;
                    case 'email':
                        $row[] = $checkin['email'] ?? '';
                        break;
                    case 'phone':
                        $row[] = $checkin['phone'] ?? '';
                        break;
                    case 'event_name':
                        $row[] = $checkin['event_name'];
                        break;
                    case 'event_date':
                        $row[] = $date ?? date('Y-m-d', strtotime($event['start_date'])) . ' to ' . date('Y-m-d', strtotime($event['end_date']));
                        break;
                    case 'checkin_date':
                        $row[] = $checkin['checkin_date'];
                        break;
                    case 'checkin_time':
                        $row[] = $checkin['checkin_time'];
                        break;
                }
            }

            $data[] = $row;
        }
    } elseif ($type === 'summary') {
        // Export summary report
        $headers = ['Date', 'Total Attendees', 'Checked In', 'Percentage'];

        // Get all dates for this event
        $dates = generateDateRange($event['start_date'], $event['end_date']);

        // Get total attendees count
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM attendees WHERE event_id = ?");
        $stmt->execute([$eventId]);
        $totalAttendees = $stmt->fetchColumn();

        foreach ($dates as $d) {
            // Get checked-in count for this date
            $stmt = $pdo->prepare("
                SELECT COUNT(DISTINCT attendee_id)
                FROM checkin_logs
                WHERE event_id = ? AND checkin_date = ?
            ");
            $stmt->execute([$eventId, $d]);
            $checkedInCount = $stmt->fetchColumn();

            // Calculate percentage
            $percentage = $totalAttendees > 0 ? round(($checkedInCount / $totalAttendees) * 100, 2) : 0;

            $data[] = [
                date('Y-m-d', strtotime($d)),
                $totalAttendees,
                $checkedInCount,
                $percentage . '%'
            ];
        }
    }

    // Export data based on format
    if ($format === 'csv') {
        exportToCSV($data, $headers, $filename);
    } else {
        exportToExcel($data, $headers, $filename);
    }

} catch (PDOException $e) {
    // Set error message and redirect
    $_SESSION['error_message'] = "Error exporting data: " . $e->getMessage();
    $_SESSION['return_url'] = "../public/export.php?event_id=$eventId";
    redirect('../public/error.php');
}