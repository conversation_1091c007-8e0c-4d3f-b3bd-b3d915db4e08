<?php
// This is a temporary script to verify password hashing
$password = 'admin123';
$stored_hash = '$2y$10$8tGmGPvk6wFyT7DVfA7KAeEYFPGvpF9J.jQwALHSbBXlwKJ3nP7Hy';

echo "<h1>Password Verification Test</h1>";
echo "<p>Password: $password</p>";
echo "<p>Stored Hash: $stored_hash</p>";
echo "<p>Verification Result: " . (password_verify($password, $stored_hash) ? "Success" : "Failed") . "</p>";

// Generate a new hash for comparison
$new_hash = password_hash($password, PASSWORD_BCRYPT);
echo "<p>New Hash: $new_hash</p>";
echo "<p>New Hash Verification: " . (password_verify($password, $new_hash) ? "Success" : "Failed") . "</p>";

// Check PHP version and loaded extensions
echo "<h2>PHP Environment</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Loaded Extensions: </p><ul>";
$extensions = get_loaded_extensions();
sort($extensions);
foreach ($extensions as $ext) {
    echo "<li>$ext</li>";
}
echo "</ul>";
?>
