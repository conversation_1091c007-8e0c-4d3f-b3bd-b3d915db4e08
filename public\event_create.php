<?php
// Include header
require_once 'includes/header.php';

// Check if user is admin
if (!isAdmin()) {
    $_SESSION['message'] = "You don't have permission to create events. Only administrators can create events.";
    $_SESSION['message_type'] = "danger";
    redirect('index.php');
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>Create New Event</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Create Event</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Event Details</h5>
                <form id="eventForm" action="../api/create_event.php" method="POST">
                    <div class="mb-3">
                        <label for="eventName" class="form-label">Event Name *</label>
                        <input type="text" class="form-control" id="eventName" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="eventDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="eventDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="startDate" class="form-label">Start Date *</label>
                            <input type="date" class="form-control" id="startDate" name="start_date" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="endDate" class="form-label">End Date *</label>
                            <input type="date" class="form-control" id="endDate" name="end_date" required>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Create Event</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>